import express from "express";
import {
  getSellerOrders,
  updateOrderWith<PERSON>eys,
  getUserOrders,
  addReviewToOffer,
  getRatingsBySeller,
  respondToReview,
  kyc,
  getAllOrders,
  updateOrderStatus,
  // getSellerTopOffers,
} from "./../controllers/orderController.js";
import { protect, seller, admin } from "../middleware/authMiddleware.js";

const router = express.Router();

// Get all orders - Admin only (for frontend integration)
router.route("/all-orders").get(protect, admin, getAllOrders);

// Test endpoint for getAllOrders (protected but no admin required - for testing)
router.route("/all-orders-test").get(protect, getAllOrders);

// Update order status - Admin only
router.route("/update-status/:orderId").patch(protect, admin, updateOrderStatus);

// Get all orders for the seller
router.route("/seller-orders").get(protect, seller, getSellerOrders);
// router.route("/seller/:sellerId/top-offers").get(getSellerTopOffers);

router.route("/user-orders").get(protect, getUserOrders);
router
  .route("/seller-orders/:orderId")
  .patch(protect, seller, updateOrderWithKeys);

router.route("/:offerId/reviews").post(protect, addReviewToOffer);
router.route("/seller/:sellerId/reviews").get(getRatingsBySeller);

// /:offerId/ratings/:reviewId/respond
router
  .route("/:offerId/ratings/:reviewId/respond")
  .post(protect, seller, respondToReview);
router.route("/kyc").post(kyc);
// Update order keys for a specific order item (only the seller can update)
// router.route("/:orderId/item/:itemId/keys").patch(protect, updateOrderKeys);

// Mark an order item as completed (only the seller can mark it as complete)
// router
//   .route("/:orderId/item/:itemId/complete")
//   .patch(protect, markOrderComplete);

export default router;
