// controllers/cartController.js
import Cart from "../models/cartModel.js";
import Offer from "../models/offerModel.js";
import Coupon from "../models/couponModel.js";
import Shop from "../models/shopModel.js";

import catchAsync from "../utils/catchAsync.js";
import AppError from "../utils/appError.js";
import Order from "./../models/OrderModel.js";
import {notifyUser} from "../services/notificationService.js";

export const addToCart = catchAsync(async (req, res, next) => {
  const { offerId, quantity } = req.body;
  const userId = req.user._id;

  // Validate offer
  const offer = await Offer.findById(offerId);
  if (!offer) {
    return next(new AppError("Offer not found", 404));
  }

  // Find or create cart
  let cart = await Cart.findOne({ user: userId });
  if (!cart) {
    cart = await Cart.create({ user: userId, items: [] });
  }

  // Check if item already exists in cart
  const itemIndex = cart.items.findIndex((item) => item.offer.equals(offerId));
  if (itemIndex > -1) {
    // Update quantity if item exists
    cart.items[itemIndex].quantity += quantity;
  } else {
    // Add new item to cart
    cart.items.push({ offer: offerId, quantity });
  }

  await cart.save();

  res.status(200).json({
    status: "success",
    data: {
      cart,
    },
  });
});
export const updateCartItem = catchAsync(async (req, res, next) => {
  const { offerId, quantity } = req.body;
  const cart = await Cart.findOne({ _id: req.params.cartId });
  if (!cart) {
    return next(new AppError("Cart not found", 404));
  }

  const itemIndex = cart.items.findIndex((item) => item.offer.equals(offerId));
  if (itemIndex === -1) {
    return next(new AppError("Item not found in cart", 404));
  }

  if (quantity <= 0) {
    // Remove item if quantity is 0 or less
    cart.items.splice(itemIndex, 1);
  } else {
    // Update quantity
    cart.items[itemIndex].quantity = quantity;
  }

  await cart.save();

  res.status(200).json({
    status: "success",
    data: {
      cart,
    },
  });
});
// export const getCartSummary = catchAsync(async (req, res, next) => {
//   const userId = req.user._id;

//   const cart = await Cart.findOne({ user: userId }).populate("items.offer");
//   if (!cart) {
//     return next(new AppError("Cart not found", 404));
//   }

//   res.status(200).json({
//     status: "success",
//     data: {
//       items: cart.items,
//     },
//   });
// });
// export const getCartSummary = catchAsync(async (req, res, next) => {
//   const userId = req.user._id;

//   // Find the cart for the user and populate related fields
//   const cart = await Cart.findOne({ user: userId }).populate({
//     path: "items.offer",
//     populate: {
//       path: "template seller",
//       select:
//         "coverImage templateName price region  category subcategory name ",
//     },
//   });

//   if (!cart) {
//     return res.status(200).json({
//       status: "success",
//       data: {
//         cart: null, // Cart ID
//         items: [],
//         summary: {
//           totalCustomerPays: 0,
//           totalActualPrice: 0,
//           totalSavings: 0,
//           totalSavingsPercentage: 0,
//         },
//       },
//     });
//   }

//   // Process each item in the cart to calculate the details
//   const cartDetails = cart.items.map((item) => {
//     const offer = item.offer;
//     const template = offer.template;

//     if (!template) {
//       throw new AppError(`Template not found for offer: ${offer._id}`, 500);
//     }

//     const actualPrice = template.price || 0; // Template price
//     const customerPays = offer.customerPays || 0; // Price customer needs to pay
//     const savings = actualPrice - customerPays; // Calculate savings
//     const savingsPercentage =
//       actualPrice > 0 ? ((savings / actualPrice) * 100).toFixed(2) : 0; // Calculate percentage savings

//     return {
//       offer: offer._id, // Offer ID
//       offerName: template.templateName, // Name of the offer from template
//       coverImage: template.coverImage, // Cover image of the template
//       sellerName: offer.seller, // Seller name
//       deliveryTime: offer.instantDelivery ? "Instant" : "Manual", // Delivery time
//       region: offer.region || template.region, // Region
//       category: offer.category || "Not assigned", // Category
//       subcategory: offer.subcategory || "Not assigned", // Subcategory
//       numberOfItems: item.quantity, // Number of items
//       customerPays, // Amount customer needs to pay
//       actualPrice, // Original price of the item
//       savings, // Amount saved
//       savingsPercentage, // Percentage saved
//     };
//   });

//   // Calculate the cart summary totals
//   const totalCustomerPays = cartDetails.reduce(
//     (sum, item) => sum + item.customerPays * item.numberOfItems,
//     0
//   );
//   const totalActualPrice = cartDetails.reduce(
//     (sum, item) => sum + item.actualPrice * item.numberOfItems,
//     0
//   );
//   const totalSavings = totalActualPrice - totalCustomerPays;
//   const totalSavingsPercentage =
//     totalActualPrice > 0
//       ? ((totalSavings / totalActualPrice) * 100).toFixed(2)
//       : 0;

//   const formatNumber = (num) => {
//     if (typeof num !== "number" || isNaN(num)) return num; // Return as-is if not a valid number
//     return parseFloat(num.toFixed(1).replace(/\.0$/, ""));
//   };
//   // Send the response
//   res.status(200).json({
//     status: "success",
//     data: {
//       cart: cart._id, // Cart ID
//       items: cartDetails,
//       summary: {
//         totalCustomerPays: formatNumber(totalCustomerPays),
//         totalActualPrice: formatNumber(totalActualPrice),
//         totalSavings: formatNumber(totalSavings),
//         totalSavingsPercentage: formatNumber(totalSavingsPercentage),
//       },
//     },
//   });
// });
export const getCartSummary = catchAsync(async (req, res, next) => {
  const userId = req.user._id;

  // Fetch the user's cart and populate necessary fields
  const cart = await Cart.findOne({ user: userId })
    .populate({
      path: "items.offer",
      populate: {
        path: "template seller",
        select:"coverImage templateName price region category subcategory name serviceFee",
      },
    })
    .populate("coupon");

  if (!cart) {
    return res.status(200).json({
      status: "success",
      data: {
        cart: null,
        items: [],
        summary: {
          totalCustomerPays: 0,
          totalActualPrice: 0,
          totalSavings: 0,
          totalSavingsPercentage: 0,
        },
      },
    });
  }

  // Process cart items
  const cartDetails = cart.items.map((item) => {
    const offer = item.offer;
    const template = offer?.template;

    if (!template) {
      throw new AppError(`Template not found for offer: ${offer._id}`, 500);
    }

    const actualPrice = template.price || 0;
    const customerPays = offer.customerPays || 0;
    const savings = actualPrice - customerPays;
    const savingsPercentage =
        actualPrice > 0 ? ((savings / actualPrice) * 100).toFixed(2) : 0;

    const quantity = item.quantity;
    const serviceFeePercentage =
        typeof template.serviceFee === "number" ? template.serviceFee : 5;

    const serviceFee = ((customerPays * quantity) * serviceFeePercentage) / 100;

    return {
      offer: offer._id,
      offerName: template.templateName,
      coverImage: template.coverImage,
      sellerName: offer.seller?.name || "Unknown Seller",
      deliveryTime: offer.instantDelivery ? "Instant" : offer.deliveryTime,
      region: offer.region || template.region,
      category: offer.category || template.category || "Not assigned",
      subcategory: offer.subcategory || template.subcategory || "Not assigned",
      numberOfItems: quantity,
      customerPays,
      actualPrice,
      savings,
      savingsPercentage,
      serviceFee,
    };
  });


  // Calculate totals
  let totalCustomerPays = cartDetails.reduce(
      (sum, item) => sum + item.customerPays * item.numberOfItems,
      0
  );
  const totalActualPrice = cartDetails.reduce(
      (sum, item) => sum + item.actualPrice * item.numberOfItems,
      0
  );
  const totalSavings = totalActualPrice - totalCustomerPays;

  const totalServiceFee = cartDetails.reduce(
      (sum, item) => sum + item.serviceFee,
      0
  );

// Apply coupon before service fee
  if (cart.coupon) {
    const { discountType, discountValue, minOrderAmount } = cart.coupon;
    if (totalActualPrice >= minOrderAmount) {
      if (discountType === "fixed") {
        totalCustomerPays = Math.max(totalCustomerPays - discountValue, 0);
      } else if (discountType === "percentage") {
        totalCustomerPays = Math.max(
            totalCustomerPays - (totalCustomerPays * discountValue) / 100,
            0
        );
      }
    }
  }

  totalCustomerPays += totalServiceFee;

  const totalSavingsPercentage =
      totalActualPrice > 0
          ? ((totalSavings / totalActualPrice) * 100).toFixed(2)
          : 0;


  const formatNumber = (num) => {
    if (typeof num !== "number" || isNaN(num)) return num;
    return parseFloat(num.toFixed(1).replace(/\.0$/, ""));
  };

  //   res.status(200).json({
  //     status: "success",
  //     data: {
  //       cart: cart._id,
  //       items: cartDetails,
  //       summary: {
  //         totalCustomerPays: formatNumber(totalCustomerPays),
  //         totalActualPrice: formatNumber(totalActualPrice),
  //         totalSavings: formatNumber(totalSavings),
  //         totalSavingsPercentage: formatNumber(totalSavingsPercentage),
  //       },
  //       appliedCoupon: cart.coupon ? cart.coupon.code : null,
  //     },
  //   });
  res.status(200).json({
    status: "success",
    data: {
      cart: cart._id, // Cart ID
      items: cartDetails,
      summary: {
        totalCustomerPays: formatNumber(totalCustomerPays),
        totalActualPrice: formatNumber(totalActualPrice),
        totalSavings: formatNumber(totalSavings),
        totalSavingsPercentage: formatNumber(totalSavingsPercentage),
        serviceFee: formatNumber(totalServiceFee)
      },
    },
  });
});

export const createOrder = catchAsync(async (req, res, next) => {
  const userId = req.user._id;
  const cart = await Cart.findOne({ user: userId }).populate("items.offer");
  if (!cart || cart.items.length === 0) {
    return next(new AppError("Cart is empty", 400));
  }

  let subtotal = 0;
  let savings = 0;

  const orderItems = cart.items.map((item) => {
    const offer = item.offer;
    subtotal += offer.customerPays * item.quantity;
    savings += (offer.expectedPrice - offer.customerPays) * item.quantity;

    return {
      offer: offer._id,
      quantity: item.quantity,
      price: offer.customerPays,
    };
  });

  const serviceFee = subtotal * 0.1; // 10% service fee
  const total = subtotal + serviceFee;

  const order = await Order.create({
    user: userId,
    items: orderItems,
    subtotal,
    serviceFee,
    total,
    savings,
    status: "review",
  });

  const populatedOrder = await Order.findById(order._id).populate('items.offer');

  populatedOrder.items.map((item) => {
    notifyUser({userId:item.offer.seller, message:"You have received an order", title:"New Order!", entityId:order._id, entityType:"order"}).finally()
  })

  //for user
  notifyUser({userId, message:"Your recent order has been initialized.", title:"Order accepted!", entityId:order._id, entityType:"order"}).finally()

  // Clear cart after order is created
  await Cart.findOneAndDelete({ user: userId });

  res.status(201).json({
    status: "success",
    data: {
      order,
    },
  });
});

// export const getPurchaseHistory = catchAsync(async (req, res, next) => {
//   const userId = req.user._id;
//   const orders = await Order.find({ user: userId, latest: true }).populate({
//     path: "items",
//     populate: {
//       path: "offer",
//       select: "name customerPays", // Only fetch 'name' and 'customerPays'
//       populate: [
//         {
//           path: "template",
//           select: "price", // Only fetch 'price'
//         },
//         {
//           path: "seller",
//           populate: {
//             path: "shop",
//           },
//         },
//       ],
//     },
//   });

//   res.status(200).json({
//     status: "success",
//     results: orders.length,
//     data: {
//       orders,
//     },
//   });
// });
export const getPurchaseHistory = catchAsync(async (req, res, next) => {
  const userId = req.user._id;
  const {limit} = req.query;

  // Fetch orders with populated seller details
  const orders = await Order.find({ user: userId, latest: true })
    .populate({
      path: "items",
      populate: {
        path: "offer",
        select: "name customerPays",
        populate: [
          {
            path: "template",
            select: "price",
          },
          {
            path: "seller",
            select: "name email avatar", // Include seller details
          },
        ],
      },
    })
    .lean()
      .limit(limit || 2);

  // Extract unique seller IDs
  const sellerIds = new Set();
  orders.forEach((order) => {
    order.items.forEach((item) => {
      if (item.offer && item.offer.seller) {
        sellerIds.add(item.offer.seller._id.toString());
      }
    });
  });

  // Fetch shops for these sellers
  const shops = await Shop.find({
    seller: { $in: Array.from(sellerIds) },
  }).lean();

  // Map shops to sellers
  const shopMap = {};
  shops.forEach((shop) => {
    shopMap[shop.seller.toString()] = shop;
  });

  // Attach shop details to the response
  orders.forEach((order) => {
    order.items.forEach((item) => {
      if (item.offer && item.offer.seller) {
        const sellerId = item.offer.seller._id.toString();
        item.offer.seller._doc.shop = shopMap[sellerId] || null;
      }
    });
  });

  res.status(200).json({
    status: "success",
    results: orders.length,
    data: {
      orders,
    },
  });
});

export const removeFromCart = catchAsync(async (req, res, next) => {
  const cartId = req.params.cartId;
  const { offerId } = req.body;

  // Validate cart
  let cart = await Cart.findById(cartId);
  if (!cart) {
    return next(new AppError("Cart not found", 404));
  }

  // Find item in the cart
  const itemIndex = cart.items.findIndex((item) => item.offer.equals(offerId));
  if (itemIndex === -1) {
    return next(new AppError("Item not found in cart", 404));
  }

  // Remove the item from the cart
  cart.items.splice(itemIndex, 1);

  // Save the updated cart
  await cart.save();

  res.status(200).json({
    status: "success",
    data: {
      cart,
    },
  });
});

export const applyCouponToCart = catchAsync(async (req, res, next) => {
  const { cartId, couponCode } = req.body;

  // Validate cartId
  if (!cartId) {
    return next(new AppError("Cart ID is required", 400));
  }

  // Find the cart using cartId
  const cart = await Cart.findById(cartId);
  if (!cart) {
    return next(new AppError("Cart not found", 404));
  }

  // Find the coupon by code
  const coupon = await Coupon.findOne({ code: couponCode, isActive: true });
  if (!coupon) {
    return next(new AppError("Invalid or inactive coupon", 400));
  }

  // Check if coupon has expired
  if (new Date() > coupon.expirationDate) {
    return next(new AppError("Coupon has expired", 400));
  }

  // Calculate total cart value to check minimum order amount
  const totalCartValue = cart.items.reduce(
    (sum, item) => sum + item.offer.customerPays * item.quantity,
    0
  );

  if (totalCartValue < coupon.minOrderAmount) {
    return next(
      new AppError(
        `Minimum order amount for this coupon is $${coupon.minOrderAmount}`,
        400
      )
    );
  }

  // Attach coupon to cart
  cart.coupon = coupon._id;
  await cart.save();

  res.status(200).json({
    status: "success",
    message: "Coupon applied successfully",
    data: {
      cartId: cart._id,
      coupon: coupon.code,
    },
  });
});
