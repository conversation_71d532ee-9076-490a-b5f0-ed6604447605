import express from "express";
import { protect, admin } from "./../middleware/authMiddleware.js";

import {
  presignedImages,
  presignedVideos,
} from "./../controllers/presignedController.js";
const router = express.Router();

// Route to create a new template (admin only)
router.route("/images/:filename").get(presignedImages);
router.route("/videos/:filename").get(presignedVideos);

export default router;
