import User from "../models/userModel.js";
import catchAsync from "../utils/catchAsync.js";
import { notifyUser } from "../services/notificationService.js";

export const keyCallbackPost = catchAsync(async (req, res) => {
    const kycData = req.body;

    if (!kycData.applicant_id || !kycData.status || !kycData.applicant?.external_applicant_id) {
        return res.status(400).json({
            status: 'error',
            message: 'Missing required fields in KYC callback'
        });
    }

    // Find the user by external_applicant_id (which is userId)
    const user = await User.findById(kycData.applicant.external_applicant_id);
    if (!user) {
        return res.status(404).json({
            status: 'error',
            message: 'User not found'
        });
    }

    // Prepare KYC details
    const kycDetails = {
        verification_id: kycData.verification_id,
        applicant_id: kycData.applicant_id,
        status: kycData.status,
        verified: kycData.verified,
        firstName: kycData.applicant.first_name,
        lastName: kycData.applicant.last_name,
        residenceCountry: kycData.applicant.residence_country,
        dateOfBirth: new Date(kycData.applicant.dob),
        lastVerificationDate: new Date(),
        documentVerified: kycData.verifications?.document?.verified || false,
        profileVerified: kycData.verifications?.profile?.verified || false,
        verificationComment: kycData.verifications?.profile?.comment || kycData.verifications?.document?.comment
    };

    console.log("KYC DETAILS", kycDetails, "------");
    console.log("KYC DAta", kycData, "-------");

    // Handle verification status
    if (kycData.verified && kycDetails.documentVerified && kycDetails.profileVerified) {
        user.isKycVerified = 'approved';
        user.kycDetails = kycDetails;
        await user.save();

        await notifyUser({
            userId: user._id,
            message: "Your KYC verification has been completed successfully. Your future orders will be processed automatically.",
            title: "KYC Verification Successful",
            entityType: "kyc",
            entityId: null
        });

        return res.status(200).json({
            status: 'success',
            message: 'KYC verification completed successfully',
            isKycVerified: 'approved'
        });
    } else {
        const declineReasons = new Set([
            ...(kycData.verifications?.profile?.decline_reasons || []),
            ...(kycData.verifications?.document?.decline_reasons || []),
            ...(kycData.applicant?.decline_reasons || [])
        ]);
        kycDetails.declineReasons = Array.from(declineReasons);

        user.isKycVerified = 'pending';
        user.kycDetails = kycDetails;
        await user.save();

        await notifyUser({
            userId: user._id,
            message: `KYC verification needs attention: ${kycDetails.verificationComment || 'Please complete the KYC process with valid information.'}`,
            title: "KYC Verification Required",
            entityType: "order",
            entityId: user._id
        });

        return res.status(200).json({
            status: 'success',
            message: 'KYC verification requires attention',
            isKycVerified: 'pending',
            declineReasons: kycDetails.declineReasons,
            verificationComment: kycDetails.verificationComment
        });
    }
});
;

export const getKycUrl = catchAsync(async (req, res) => {

    const userId = req.user._id;

    const user = await User.findById(userId);

    const applicantBody = {
        type: "PERSON",
        first_name: user.name,
        email: user.email,
        external_applicant_id: user._id,
    }

    const applicantResponse = await fetch(
        `https://api.kycaid.com/applicants`,
        {
            method: 'POST',
            headers: {
                'Authorization': `Token ${process.env.KYCAID_SECRET_KEY}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(applicantBody)
        }
    );

    const applicantJson = await applicantResponse.json();

    const formBody = {
        redirect_url: `${process.env.FRONTEND_URL}`,
        external_applicant_id: user._id,
        ...applicantJson,
    }

    const response = await fetch(
        `https://api.kycaid.com/forms/${process.env.KYCAID_FORM_ID}/urls`,
        {
            method: 'POST',
            headers: {
                'Authorization': `Token ${process.env.KYCAID_SECRET_KEY}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formBody)
        }
    );

    const responseData = await response.json();

    return res.status(200).json({
        status: 'success',
        ...responseData,
        formBody
    });

})
