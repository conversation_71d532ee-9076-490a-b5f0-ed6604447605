import catchAsync from "../utils/catchAsync.js";
import AppError from "../utils/appError.js";
import APIFeatures from "../utils/apiFeatures.js";
import Banner from "../models/bannerModel.js";

// Create a new banner
export const createBanner = catchAsync(async (req, res, next) => {
  const newBanner = await Banner.create(req.body);

  res.status(201).json({
    status: "success",
    data: {
      banner: newBanner,
    },
  });
});

// Get a single banner by ID
export const getBanner = catchAsync(async (req, res, next) => {
  const banner = await Banner.findById(req.params.id);

  if (!banner) {
    return next(new AppError("No banner found with that ID", 404));
  }

  res.status(200).json({
    status: "success",
    data: {
      banner,
    },
  });
});

// Get all banners (with filtering, sorting, and pagination)
export const getAllBanners = catchAsync(async (req, res, next) => {
  const features = new APIFeatures(Banner.find(), req.query)
    .filter()
    .sort()
    .paginate();

  const banners = await features.query;

  res.status(200).json({
    status: "success",
    results: banners.length,

    data: {
      banners,
    },
  });
});

// Update a banner by ID
export const updateBanner = catchAsync(async (req, res, next) => {
  const banner = await Banner.findByIdAndUpdate(req.params.id, req.body, {
    new: true,
    runValidators: true,
  });

  if (!banner) {
    return next(new AppError("No banner found with that ID", 404));
  }

  res.status(200).json({
    status: "success",
    data: {
      banner,
    },
  });
});

// Delete a banner by ID
export const deleteBanner = catchAsync(async (req, res, next) => {
  const banner = await Banner.findByIdAndDelete(req.params.id);

  if (!banner) {
    return next(new AppError("No banner found with that ID", 404));
  }

  res.status(200).json({
    status: "success",
    data: null,
  });
});
