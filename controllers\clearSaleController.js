import catchAsync from "../utils/catchAsync.js";
import Order from "../models/OrderModel.js";
import User from "../models/userModel.js";
import { notifyUser } from "../services/notificationService.js";
import sendgrid from "@sendgrid/mail";
import { orderCompletionMail } from "../utils/emailTemplates/templates.js";
sendgrid.setApiKey(process.env.EMAIL_API_KEY);

export const sendOrder = catchAsync(async (req, res, next) => {
    // First check if user is KYC verified
    const user = await User.findById(req.user._id);
    if (!user) {
        return res.status(404).json({
            status: 'error',
            message: 'User not found'
        });
    }

    // If user is KYC verified (approved), auto-approve without calling ClearSale API
    if (user.isKycVerified === 'approved') {
        // Find the order and update its status
        const order = await Order.findOne({ orderNumber: req.body.ID }).populate({
            path: 'items.offer',
            select: 'instantDelivery licenseKeys stock sold usedKeys'
        });

        if (!order) {
            return res.status(404).json({
                status: 'error',
                message: 'Order not found'
            });
        }

        // Process instant delivery items for KYC verified users
        for (const item of order.items) {
            const offer = item.offer;
            
            if (offer.instantDelivery) {
                // Check if enough keys are available
                if (offer.licenseKeys.length < item.quantity) {
                    console.error(`Insufficient keys for offer ${offer._id}`);
                    continue;
                }

                // Transfer keys from offer to order item and track used keys
                const keysToTransfer = offer.licenseKeys.splice(0, item.quantity);
                item.keys = keysToTransfer;

                // Track used keys in the offer
                offer.usedKeys.push(...keysToTransfer.map(key => ({
                    key,
                    orderId: order._id,
                })));

                // Update offer stock
                offer.stock = offer.licenseKeys.length;
                offer.sold += item.quantity;
                await offer.save();

                // Update item status
                item.status = "completed";
            }
        }

        // Update order status
        order.status = "completed";
        await order.save();

        // Notify user about instant approval
        await notifyUser({
            userId: order.user,
            message: "Your order has been automatically approved as you are KYC verified.",
            title: "Order Approved",
            entityId: order._id,
            entityType: "order"
        });

        return res.status(200).json({
            status: 'success',
            data: {
                Status: 'APA', // Auto-approve for KYC verified users
                Score: 100,
                message: 'Order auto-approved - KYC verified user'
            }
        });
    }

    // If user's KYC is pending, return appropriate response
    if (user.isKycVerified === 'approved') {
        return res.status(200).json({
            status: 'success',
            data: {
                Status: 'Automatically Approved', // Review required
                Score: 0,
                message: 'KYC verification Approved , No ClearSale API call required'
            }
        });
    }

    // If user is not KYC verified (idle), proceed with ClearSale API call
    const apiKey = process.env.CLEARSALE_API_KEY;

    const payload = {
        ApiKey: apiKey,
        ...req.body,
    };

    const response = await fetch(`${process.env.CLEARSALE_API_BASE}/order/send`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
    });

    const data = await response.json();

    res.status(200).json({
        status: 'success',
        data
    });
});

export const clearSaleCallback = catchAsync(async (req, res, next) => {
    const { ID, Status, Score } = req.body;
    
    console.log("Received ClearSale status update:", { ID, Status, Score });
    // Find the order by ClearSale ID
    const order = await Order.findOne({ orderNumber: ID }).populate({
        path: 'items.offer',
        select: 'instantDelivery licenseKeys stock sold usedKeys'
    });

    if (!order) {
        console.error("Order not found for ClearSale ID:", ID);
        return res.status(404).json({ error: "Order not found" });
    }

    // Find the user and update KYC status
    const user = await User.findById(order.user);
    if (!user) {
        console.error("User not found for order:", ID);
        return res.status(404).json({ error: "User not found" });
    }

    // Update order status based on ClearSale response
    if (Status === "APA" || Status === "APM") { // Approved

        // Process instant delivery items
        for (const item of order.items) {
            const offer = item.offer;
            
            if (offer.instantDelivery) {
                // Check if enough keys are available
                if (offer.licenseKeys.length < item.quantity) {
                    console.error(`Insufficient keys for offer ${offer._id}`);
                    continue;
                }

                // Transfer keys from offer to order item and track used keys
                const keysToTransfer = offer.licenseKeys.splice(0, item.quantity);
                item.keys = keysToTransfer;

                // Track used keys in the offer
                offer.usedKeys.push(...keysToTransfer.map(key => ({
                    key,
                    orderId: order._id,
                })));

                // Update offer stock
                offer.stock = offer.licenseKeys.length;
                offer.sold += item.quantity;
                await offer.save();

                // Update item status
                item.status = "completed";
            }
        }

        // Update order status
        order.status = "completed";

        // Capture payment if payment data exists
        if (order.paymentData && order.paymentData.status === 'pending') {
            try {
                const captureRes = await fetch(order.paymentData.captureUrl, {
                    method: "POST",
                    headers: {
                        Authorization: `Bearer ${process.env.SWEDBANK_API_TOKEN}`,
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify({
                        transaction: {
                            amount: order.total * 100,
                            vatAmount: order.paymentData.vatAmount || 0,
                            description: `Capture for order ${order.orderNumber}`,
                            payeeReference: `capture${Date.now()}`,
                        },
                    }),
                });

                if (!captureRes.ok) {
                    console.error("Payment capture failed:", await captureRes.text());
                    order.paymentData.status = 'failed';
                } else {
                    order.paymentData = null;
                }
            } catch (error) {
                console.error("Error capturing payment:", error);
                order.paymentData.status = 'failed';
            }
        }

        await order.save();

        // Get user details for the email
        const userDetails = await User.findById(order.user);
        
        // Check if all items are completed before sending email
        const allItemsCompleted = order.items.every(item => item.status === "completed");
        
        if (allItemsCompleted) {
            // Send completion email
            const mailOptions = orderCompletionMail(order, userDetails);
            mailOptions.to = userDetails.email;
            mailOptions.from = process.env.EMAIL_FROM;
            
            try {
                await sendgrid.send(mailOptions);
            } catch (error) {
                console.error("Error sending order completion email:", error);
                // Don't throw error, continue with notifications
            }
        }

        // Notify user about order completion
        await notifyUser({
            userId: order.user,
            message: "Your order has been approved and completed.",
            title: "Order Approved",
            entityId: order._id,
            entityType: "order"
        });

    } else if (Status === "RPM" || Status === "RPA") { // Declined
        // Update user's KYC status to pending
        user.isKycVerified = 'pending';
        await user.save();

        order.status = "cancelled";
        for (const item of order.items) item.status = "cancelled";
        await order.save();

        await notifyUser({
            userId: order.user,
            message: "Your order has been declined. Please complete the KYC form.",
            title: "KYC Required",
            entityId: order._id,
            entityType: "order"
        });
    } else { // Review
        // Update user's KYC status to pending
        user.isKycVerified = 'pending';
        await user.save();

        order.status = "review";
        await order.save();

        await notifyUser({
            userId: order.user,
            message: "Your order is under review. Please complete the KYC form.",
            title: "KYC Required",
            entityId: order._id,
            entityType: "order"
        });
    }

    res.status(200).json({
        status: "success",
        message: "Order status updated successfully",
        kycStatus: user.isKycVerified
    });
});