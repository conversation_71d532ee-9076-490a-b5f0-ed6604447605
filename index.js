import cors from "cors";
import express from "express";
import * as dotenv from "dotenv";
import cron from "node-cron";
// import fileUpload from 'express-fileupload'
import { globalErrorHandler } from "./controllers/errorController.js";
import AppError from "./utils/appError.js";
import connectDB from "./config/db.js";
import userRoutes from "./routes/userRoutes.js";
import requestRoutes from "./routes/reqeustRoutes.js";
import subcategoryRoutes from "./routes/subcategoryRoutes.js";
import templateRoutes from "./routes/templateRoutes.js";
import presignedRoutes from "./routes/presignedRoutes.js";
import offerRoutes from "./routes/offerRoutes.js";
import bannerRoutes from "./routes/bannerRoutes.js";
import homeConfigRoutes from "./routes/homeConfigRoutes.js";
import cartRoutes from "./routes/cartRoutes.js";
import paymentRoutes from "./routes/paymentRoutes.js";
import couponRoutes from "./routes/couponRoutes.js";
import blogCategoryRoutes from "./routes/blogCategoryRoutes.js";
import blogRoutes from "./routes/blogRoutes.js";
import shopOpeningRequestRoutes from "./routes/shopOpeningRequestRoutes.js";
import shopRoutes from "./routes/shopRoutes.js";
import ticketRoutes from "./routes/ticketRoutes.js";
import faqRoutes from "./routes/faqRoutes.js";
import helpRoutes from "./routes/helpRotues.js";
import contactRoutes from "./routes/contactRoutes.js";
import orderRoutes from "./routes/orderRoutes.js";
import wishListRoutes from "./routes/wishListRoutes.js";
import conversationRoutes from "./routes/conversationRoutes.js";
import newsLetterRoutes from "./routes/newsLetterRoutes.js";
import messsageRoutes from "./routes/messageRoute.js";
import { inactiveUserMail } from "./utils/emailTemplates/templates.js";
import { inactiveUser } from "./utils/emailTemplates/inActiveUser.js";
import pusherRoutes from "./routes/pusherRoutes.js";
import notificationRoutes from "./routes/notificationRoutes.js";
import taxRoutes from "./routes/taxRoutes.js";
import giveAwayRoutes from "./routes/giveAwayRoutes.js";
import levelRoutes from "./routes/levelRoutes.js";
import clearSaleRoutes from "./routes/clearSaleRoutes.js";
import kycRoutes from "./routes/kycRoutes.js";
import reportRoutes from "./routes/reportRoutes.js";
import watchListRoutes from "./routes/watchlistRoutes.js";

// ------------------------------
// Init Express App
// ------------------------------
dotenv.config(); // Load ENV
connectDB().finally(); // connect DB

const app = express();
app.use(express.json()); // For parsing application/json
app.use(express.urlencoded({ extended: true })); // For parsing application/x-www-form-urlencoded
app.use(cors());
// app.use(fileUpload())

// ------------------------------
// routes
// ------------------------------
app.use("/users", userRoutes);
app.use("/requests", requestRoutes);
app.use("/sub-catagory", subcategoryRoutes);
app.use("/templates", templateRoutes);
app.use("/presigned-url", presignedRoutes);
app.use("/offers", offerRoutes);
app.use("/banners", bannerRoutes);
app.use("/homepage-config", homeConfigRoutes);
app.use("/cart", cartRoutes);
app.use("/payment", paymentRoutes);
app.use("/coupon", couponRoutes);
app.use("/blog-category", blogCategoryRoutes);
app.use("/blog", blogRoutes);
app.use("/shop-opening-requests", shopOpeningRequestRoutes);
app.use("/shop", shopRoutes);
app.use("/tickets", ticketRoutes);
app.use("/faqs", faqRoutes);
app.use("/help", helpRoutes);
app.use("/support", contactRoutes);
app.use("/order", orderRoutes);
app.use("/wish-list", wishListRoutes);
app.use("/news-letter", newsLetterRoutes);
app.use("/pusher", pusherRoutes);
app.use("/notifications", notificationRoutes);
app.use("/chat", conversationRoutes);
app.use("/message", messsageRoutes);
app.use("/tax", taxRoutes);
app.use("/give-away", giveAwayRoutes);
app.use("/levels", levelRoutes);
app.use("/clear-sale", clearSaleRoutes);
app.use("/kyc", kycRoutes);
app.use("/reports", reportRoutes);
app.use("/watchlist", watchListRoutes);

// To deal all request whose URL is not specified in the server
app.all("*", (req, res, next) => {
  next(new AppError(`Can't find ${req.originalUrl} on this server!`, 404));
});
app.use(globalErrorHandler);

// ------------------------------
// Listen at port 4000 || 8080
// ------------------------------
const port = parseInt(process.env.PORT) || 8080;
app.listen(port, "0.0.0.0", () => {
  console.log(`vbrae listening on port ${port}`);
});

cron.schedule("0 10 * * *", () => {
  inactiveUser(inactiveUserMail());
});
