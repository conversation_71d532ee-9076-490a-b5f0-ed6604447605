import mongoose from "mongoose";

const wishlistSchema = new mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    offer: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Offer",
      required: true,
    },
  },
  {
    timestamps: true,
    // Ensure each user can only have an offer in their wishlist once
    index: { unique: [true, "Offer already in wishlist"] },
  }
);

// Compound index to prevent duplicate entries
wishlistSchema.index({ user: 1, offer: 1 }, { unique: true });

const Wishlist = mongoose.model("Wishlist", wishlistSchema);

export default Wishlist;
