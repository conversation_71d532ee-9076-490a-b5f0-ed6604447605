import express from "express";
import multer from "multer";
import path from "path";
const router = express.Router();
import { fileURLToPath } from "url";
import {
  createSubCategory,
  getSubCategoryById,
  getAllSubCategories,
  updateSubCategory,
  deleteSubCategory,
  bulkUploadCategories,
} from "./../controllers/subCategoryController.js";
import { admin, protect, seller } from "./../middleware/authMiddleware.js";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Set storage configuration for Multer
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, path.join(__dirname, "./../uploads/")); // Adjust path for top-level uploads folder
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
    cb(
      null,
      file.fieldname + "-" + uniqueSuffix + path.extname(file.originalname)
    );
  },
});

// Configure Multer
const upload = multer({
  storage,
  limits: { fileSize: 5 * 1024 * 1024 }, // 5 MB limit
});
// Route to create a new subcategory (admin only)
router.route("/").post(protect, admin, createSubCategory);

// Route to get a subcategory by ID (accessible by authenticated users)
router.route("/:id").get(getSubCategoryById);

// Route to get all subcategories (admin only)
router.route("/").get(getAllSubCategories);

// Route to update a subcategory (admin only)
router.route("/:id").patch(protect, admin, updateSubCategory);

// Route to delete a subcategory (admin only)
router.route("/:id").delete(protect, admin, deleteSubCategory);

// Route for bulk uploading categories
router.post("/csv", upload.single("csvFile"), bulkUploadCategories);

export default router;
