import Level from "../models/levelModel.js";

export const levelsData = [
  {
    name: "New Seller",
    order: 1,
    fromOrders: 0,
    toOrders: 49,
    earningReleaseDays: 21,
    commissionPercent: 10,
  },
  {
    name: "Level 2",
    order: 2,
    fromOrders: 50,
    toOrders: 149,
    earningReleaseDays: 17,
    commissionPercent: 10,
  },
  {
    name: "Level 3",
    order: 3,
    fromOrders: 150,
    toOrders: 299,
    earningReleaseDays: 12,
    commissionPercent: 10,
  },
  {
    name: "Level 4",
    order: 4,
    fromOrders: 300,
    toOrders: 599,
    earningReleaseDays: 9,
    commissionPercent: 9,
  },
  {
    name: "Level 5",
    order: 5,
    fromOrders: 600,
    toOrders: 999,
    earningReleaseDays: 7,
    commissionPercent: 8,
  },
  {
    name: "Top Level",
    order: 6,
    fromOrders: 1000,
    toOrders: null,
    earningReleaseDays: 5,
    commissionPercent: 7,
  },
];

export const createLevel = async (req, res) => {
  try {
    const bulkOps = levelsData.map(
        ({ name, order, fromOrders, toOrders, earningReleaseDays, commissionPercent }) => ({
          updateOne: {
            filter: { order },
            update: {
              $set: {
                name,
                fromOrders,
                toOrders,
                earningReleaseDays,
                commissionPercent,
              },
            },
            upsert: true,
          },
        })
    );

    await Level.bulkWrite(bulkOps);
    const updatedLevels = await Level.find().sort({ order: 1 });

    res.status(201).json({ status: "success", data: updatedLevels });
  } catch (error) {
    console.error(error);
    res.status(500).json({ status: "error", message: "Failed to save levels" });
  }
};


export const getLevels = async (req, res, next) => {
  try {
    const { search, page, limit } = req.query;
    let query = {};
    if (search) {
      query.name = { $regex: search, $options: "i" };
    }
    let levelQuery = Level.find(query);
    levelQuery = levelQuery.sort({order: 1});
    let total = await Level.countDocuments(query);
    let results, pagination = undefined;
    if (page && limit) {
      const pageNum = parseInt(page, 10) || 1;
      const limitNum = parseInt(limit, 10) || 10;
      const skip = (pageNum - 1) * limitNum;
      levelQuery = levelQuery.skip(skip).limit(limitNum);
      results = await levelQuery;
      pagination = {
        currentPage: pageNum,
        pages: Math.ceil(total / limitNum),
      };
    } else {
      results = await levelQuery;
    }
    res.status(200).json({ status: "success", results: results.length, pagination, data: results });
  } catch (err) {
    next(err);
  }
};

export const getLevel = async (req, res, next) => {
  try {
    const level = await Level.findById(req.params.id);
    if (!level) return res.status(404).json({ status: "fail", message: "Level not found" });
    res.status(200).json({ status: "success", data: level });
  } catch (err) {
    next(err);
  }
};

export const updateLevel = async (req, res, next) => {
  try {
    const level = await Level.findByIdAndUpdate(req.params.id, req.body, { new: true, runValidators: true });
    if (!level) return res.status(404).json({ status: "fail", message: "Level not found" });
    res.status(200).json({ status: "success", data: level });
  } catch (err) {
    if (err.message && err.message.includes("Order range overlaps with an existing level.")) {
      return res.status(422).json({ status: "fail", message: err.message });
    }
    next(err);
  }
};

export const deleteLevel = async (req, res, next) => {
  try {
    const level = await Level.findByIdAndDelete(req.params.id);
    if (!level) return res.status(404).json({ status: "fail", message: "Level not found" });
    res.status(204).json({ status: "success", data: null });
  } catch (err) {
    next(err);
  }
}; 