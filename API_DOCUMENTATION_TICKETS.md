# Ticket System API Documentation

## Overview
This documentation covers the ticket system APIs for your frontend application. The system supports both authenticated user tickets and guest tickets, with conversation integration for support chat.

## Ticket Model Structure

```javascript
{
  "_id": "ticket_id",
  "name": "<PERSON>", // Default "Guest" for non-logged users
  "email": "<EMAIL>",
  "subject": "Issue with my order",
  "inquiryType": "Order Issue", // Category of inquiry
  "description": "Detailed description of the issue",
  "attachments": ["url1", "url2"], // Array of attachment URLs
  "status": "open", // "open", "responded", "closed"
  "ticketNumber": "TICKET-ABC12345", // Auto-generated unique number
  "user": "user_id", // Optional - null for guest tickets
  "conversationId": "conversation_id", // Link to chat conversation
  "createdAt": "2024-01-15T10:00:00.000Z",
  "updatedAt": "2024-01-15T10:00:00.000Z"
}
```

## API Endpoints

### 1. Get User's Tickets
**Endpoint:** `GET /tickets/my-tickets`
**Authentication:** Required (<PERSON><PERSON>)
**Description:** Get all tickets created by the authenticated user

#### Query Parameters
- `page` (number, default: 1) - Page number
- `limit` (number, default: 10, max: 50) - Items per page
- `status` (string) - Filter by status: `open`, `responded`, `closed`
- `inquiryType` (string) - Filter by inquiry type
- `search` (string) - Search in subject, description, or ticket number
- `sortBy` (string, default: 'createdAt') - Sort field
- `sortOrder` (string, default: 'desc') - Sort direction: `asc`, `desc`

#### Example Request
```bash
curl -X GET "http://localhost:4000/tickets/my-tickets?page=1&limit=10&status=open&search=order" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### Response Format
```json
{
  "status": "success",
  "results": 5,
  "pagination": {
    "currentPage": 1,
    "totalPages": 2,
    "total": 15,
    "hasNextPage": true,
    "hasPrevPage": false,
    "limit": 10
  },
  "data": [
    {
      "_id": "ticket_id",
      "name": "John Doe",
      "email": "<EMAIL>",
      "subject": "Order Issue",
      "inquiryType": "Order Support",
      "description": "I have an issue with my recent order...",
      "attachments": ["https://example.com/image1.jpg"],
      "status": "open",
      "ticketNumber": "TICKET-ABC12345",
      "user": {
        "_id": "user_id",
        "firstName": "John",
        "lastName": "Doe",
        "email": "<EMAIL>",
        "avatar": "avatar_url"
      },
      "conversationId": {
        "_id": "conversation_id",
        "lastMessage": "Thank you for contacting us",
        "unreadClient": 1,
        "unreadSeller": 0
      },
      "createdAt": "2024-01-15T10:00:00.000Z",
      "updatedAt": "2024-01-15T10:00:00.000Z"
    }
  ]
}
```

### 2. Get Single Ticket Details
**Endpoint:** `GET /tickets/:id`
**Authentication:** Required (Bearer Token)
**Description:** Get detailed information about a specific ticket

#### Query Parameters (for guest tickets)
- `email` (string) - Required for guest tickets to verify access

#### Example Request
```bash
# For user tickets
curl -X GET "http://localhost:4000/tickets/TICKET_ID" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# For guest tickets
curl -X GET "http://localhost:4000/tickets/TICKET_ID?email=<EMAIL>" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### Response Format
```json
{
  "status": "success",
  "data": {
    "_id": "ticket_id",
    "name": "John Doe",
    "email": "<EMAIL>",
    "subject": "Order Issue",
    "inquiryType": "Order Support",
    "description": "Detailed description of the issue...",
    "attachments": [
      "https://example.com/screenshot1.jpg",
      "https://example.com/screenshot2.jpg"
    ],
    "status": "responded",
    "ticketNumber": "TICKET-ABC12345",
    "user": {
      "_id": "user_id",
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>",
      "avatar": "https://example.com/avatar.jpg",
      "role": "user"
    },
    "conversationId": {
      "_id": "conversation_id",
      "client": {
        "_id": "user_id",
        "firstName": "John",
        "lastName": "Doe",
        "email": "<EMAIL>",
        "avatar": "https://example.com/avatar.jpg",
        "role": "user"
      },
      "seller": {
        "_id": "admin_id",
        "firstName": "Support",
        "lastName": "Team",
        "email": "<EMAIL>",
        "avatar": "https://example.com/support-avatar.jpg",
        "role": "admin"
      },
      "lastMessage": "We've resolved your issue",
      "unreadClient": 0,
      "unreadSeller": 0,
      "type": "ticket"
    },
    "createdAt": "2024-01-15T10:00:00.000Z",
    "updatedAt": "2024-01-15T12:30:00.000Z"
  }
}
```

### 3. Create New Ticket
**Endpoint:** `POST /tickets`
**Authentication:** Optional (can be used by guests)
**Description:** Create a new support ticket

#### Request Body
```json
{
  "name": "John Doe", // Optional - defaults to "Guest"
  "email": "<EMAIL>", // Required
  "subject": "Issue with my order", // Required
  "inquiryType": "Order Support", // Required
  "description": "Detailed description...", // Required
  "attachments": ["url1", "url2"] // Optional array of URLs
}
```

#### Example Request
```bash
curl -X POST "http://localhost:4000/tickets" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "John Doe",
    "email": "<EMAIL>",
    "subject": "Order Issue",
    "inquiryType": "Order Support",
    "description": "I need help with my recent order",
    "attachments": []
  }'
```

### 4. Get All Tickets (Admin Only)
**Endpoint:** `GET /tickets`
**Authentication:** Required (Bearer Token)
**Authorization:** Admin role required
**Description:** Get all tickets in the system with pagination

#### Query Parameters
Same as user tickets endpoint, plus:
- No user filtering (shows all tickets)

### 5. Update Ticket Status
**Endpoint:** `PATCH /tickets/:id`
**Authentication:** Required (Bearer Token)
**Description:** Update ticket status or other fields

#### Request Body
```json
{
  "status": "closed"
}
```

## Frontend Integration Examples

### React/JavaScript Example

```javascript
// Ticket service
class TicketService {
  constructor(baseURL, getToken) {
    this.baseURL = baseURL;
    this.getToken = getToken;
  }

  async getUserTickets(filters = {}) {
    try {
      const queryParams = new URLSearchParams(filters).toString();
      const response = await fetch(`${this.baseURL}/tickets/my-tickets?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${this.getToken()}`,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();
      
      if (data.status === 'success') {
        return data;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      console.error('Error fetching tickets:', error);
      throw error;
    }
  }

  async getTicketById(ticketId, email = null) {
    try {
      const url = email 
        ? `${this.baseURL}/tickets/${ticketId}?email=${email}`
        : `${this.baseURL}/tickets/${ticketId}`;
        
      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${this.getToken()}`,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();
      
      if (data.status === 'success') {
        return data.data;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      console.error('Error fetching ticket:', error);
      throw error;
    }
  }

  async createTicket(ticketData) {
    try {
      const response = await fetch(`${this.baseURL}/tickets`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.getToken()}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(ticketData)
      });

      const data = await response.json();
      
      if (data.status === 'success') {
        return data.data;
      } else {
        throw new Error(data.message);
      }
    } catch (error) {
      console.error('Error creating ticket:', error);
      throw error;
    }
  }
}

// Usage in React component
const TicketList = () => {
  const [tickets, setTickets] = useState([]);
  const [pagination, setPagination] = useState({});
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState({
    page: 1,
    limit: 10,
    status: '',
    search: ''
  });

  const ticketService = new TicketService('/api', () => localStorage.getItem('token'));

  useEffect(() => {
    fetchTickets();
  }, [filters]);

  const fetchTickets = async () => {
    setLoading(true);
    try {
      const response = await ticketService.getUserTickets(filters);
      setTickets(response.data);
      setPagination(response.pagination);
    } catch (error) {
      console.error('Error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      {/* Filter controls */}
      <div className="filters">
        <input
          type="text"
          placeholder="Search tickets..."
          value={filters.search}
          onChange={(e) => setFilters({...filters, search: e.target.value, page: 1})}
        />
        <select
          value={filters.status}
          onChange={(e) => setFilters({...filters, status: e.target.value, page: 1})}
        >
          <option value="">All Status</option>
          <option value="open">Open</option>
          <option value="responded">Responded</option>
          <option value="closed">Closed</option>
        </select>
      </div>

      {/* Ticket list */}
      {loading ? (
        <div>Loading...</div>
      ) : (
        <div className="ticket-list">
          {tickets.map(ticket => (
            <div key={ticket._id} className="ticket-item">
              <h3>{ticket.subject}</h3>
              <p>Ticket: {ticket.ticketNumber}</p>
              <p>Status: {ticket.status}</p>
              <p>Created: {new Date(ticket.createdAt).toLocaleDateString()}</p>
              {ticket.conversationId?.unreadClient > 0 && (
                <span className="unread-badge">{ticket.conversationId.unreadClient} new messages</span>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Pagination */}
      <div className="pagination">
        <button 
          disabled={!pagination.hasPrevPage}
          onClick={() => setFilters({...filters, page: filters.page - 1})}
        >
          Previous
        </button>
        <span>Page {pagination.currentPage} of {pagination.totalPages}</span>
        <button 
          disabled={!pagination.hasNextPage}
          onClick={() => setFilters({...filters, page: filters.page + 1})}
        >
          Next
        </button>
      </div>
    </div>
  );
};
```

### Vue.js Example

```javascript
// Vue 3 Composition API
<template>
  <div class="ticket-system">
    <!-- Ticket List -->
    <div class="ticket-list">
      <div class="filters">
        <input
          v-model="filters.search"
          @input="debouncedSearch"
          placeholder="Search tickets..."
        />
        <select v-model="filters.status" @change="fetchTickets">
          <option value="">All Status</option>
          <option value="open">Open</option>
          <option value="responded">Responded</option>
          <option value="closed">Closed</option>
        </select>
      </div>

      <div v-if="loading" class="loading">Loading tickets...</div>

      <div v-else class="tickets">
        <div
          v-for="ticket in tickets"
          :key="ticket._id"
          class="ticket-card"
          @click="viewTicket(ticket._id)"
        >
          <div class="ticket-header">
            <h3>{{ ticket.subject }}</h3>
            <span :class="`status-${ticket.status}`">{{ ticket.status }}</span>
          </div>
          <p class="ticket-number">{{ ticket.ticketNumber }}</p>
          <p class="ticket-type">{{ ticket.inquiryType }}</p>
          <p class="ticket-date">{{ formatDate(ticket.createdAt) }}</p>

          <div v-if="ticket.conversationId?.unreadClient > 0" class="unread-indicator">
            {{ ticket.conversationId.unreadClient }} new messages
          </div>
        </div>
      </div>

      <!-- Pagination -->
      <div class="pagination" v-if="pagination.totalPages > 1">
        <button
          @click="changePage(pagination.currentPage - 1)"
          :disabled="!pagination.hasPrevPage"
        >
          Previous
        </button>

        <span>Page {{ pagination.currentPage }} of {{ pagination.totalPages }}</span>

        <button
          @click="changePage(pagination.currentPage + 1)"
          :disabled="!pagination.hasNextPage"
        >
          Next
        </button>
      </div>
    </div>

    <!-- Ticket Detail Modal -->
    <div v-if="selectedTicket" class="ticket-detail-modal" @click="closeModal">
      <div class="modal-content" @click.stop>
        <button class="close-btn" @click="closeModal">&times;</button>

        <div class="ticket-detail">
          <h2>{{ selectedTicket.subject }}</h2>
          <div class="ticket-meta">
            <span>Ticket: {{ selectedTicket.ticketNumber }}</span>
            <span>Status: {{ selectedTicket.status }}</span>
            <span>Type: {{ selectedTicket.inquiryType }}</span>
            <span>Created: {{ formatDate(selectedTicket.createdAt) }}</span>
          </div>

          <div class="ticket-description">
            <h4>Description:</h4>
            <p>{{ selectedTicket.description }}</p>
          </div>

          <div v-if="selectedTicket.attachments?.length" class="attachments">
            <h4>Attachments:</h4>
            <div class="attachment-list">
              <a
                v-for="(attachment, index) in selectedTicket.attachments"
                :key="index"
                :href="attachment"
                target="_blank"
                class="attachment-link"
              >
                Attachment {{ index + 1 }}
              </a>
            </div>
          </div>

          <div v-if="selectedTicket.conversationId" class="conversation-link">
            <button @click="openChat(selectedTicket.conversationId._id)" class="chat-btn">
              Open Chat
              <span v-if="selectedTicket.conversationId.unreadClient > 0" class="unread-count">
                ({{ selectedTicket.conversationId.unreadClient }} unread)
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { debounce } from 'lodash'

// Reactive data
const tickets = ref([])
const selectedTicket = ref(null)
const loading = ref(false)
const pagination = ref({})

const filters = reactive({
  page: 1,
  limit: 10,
  status: '',
  search: '',
  sortBy: 'createdAt',
  sortOrder: 'desc'
})

// API service
const ticketAPI = {
  async getUserTickets(params) {
    const queryString = new URLSearchParams(params).toString()
    const response = await fetch(`/api/tickets/my-tickets?${queryString}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      }
    })
    return response.json()
  },

  async getTicketById(ticketId) {
    const response = await fetch(`/api/tickets/${ticketId}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      }
    })
    return response.json()
  }
}

// Methods
const fetchTickets = async () => {
  loading.value = true
  try {
    const response = await ticketAPI.getUserTickets(filters)
    if (response.status === 'success') {
      tickets.value = response.data
      pagination.value = response.pagination
    }
  } catch (error) {
    console.error('Error fetching tickets:', error)
  } finally {
    loading.value = false
  }
}

const viewTicket = async (ticketId) => {
  try {
    const response = await ticketAPI.getTicketById(ticketId)
    if (response.status === 'success') {
      selectedTicket.value = response.data
    }
  } catch (error) {
    console.error('Error fetching ticket details:', error)
  }
}

const closeModal = () => {
  selectedTicket.value = null
}

const changePage = (newPage) => {
  filters.page = newPage
  fetchTickets()
}

const debouncedSearch = debounce(() => {
  filters.page = 1
  fetchTickets()
}, 500)

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString()
}

const openChat = (conversationId) => {
  // Navigate to chat interface
  // This depends on your routing setup
  console.log('Opening chat for conversation:', conversationId)
}

// Watchers
watch(() => filters.status, () => {
  filters.page = 1
  fetchTickets()
})

// Lifecycle
onMounted(() => {
  fetchTickets()
})
</script>

<style scoped>
.ticket-system {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.filters {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.filters input, .filters select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.ticket-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: box-shadow 0.2s;
}

.ticket-card:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.ticket-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.status-open { color: #f39c12; }
.status-responded { color: #3498db; }
.status-closed { color: #27ae60; }

.unread-indicator {
  background: #e74c3c;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  display: inline-block;
  margin-top: 8px;
}

.ticket-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  padding: 24px;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
}

.close-btn {
  position: absolute;
  top: 12px;
  right: 16px;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
}

.ticket-meta {
  display: flex;
  gap: 16px;
  margin: 16px 0;
  font-size: 14px;
  color: #666;
}

.chat-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 16px;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-top: 24px;
}

.pagination button {
  padding: 8px 16px;
  border: 1px solid #ddd;
  background: white;
  cursor: pointer;
  border-radius: 4px;
}

.pagination button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
```

## Error Handling

### Common Error Responses

```json
// Authentication Error
{
  "status": "error",
  "message": "Not authorized, no token"
}

// Authorization Error
{
  "status": "fail",
  "message": "You can only access your own tickets"
}

// Validation Error
{
  "status": "fail",
  "message": "Validation error",
  "errors": {
    "email": "Email is required",
    "subject": "Subject is required"
  }
}

// Not Found Error
{
  "status": "fail",
  "message": "No ticket found with that ID"
}
```

## Status Workflow

1. **open** - Initial status when ticket is created
2. **responded** - Admin has replied to the ticket
3. **closed** - Ticket is resolved and closed

## Integration with Chat System

Tickets automatically create conversations for real-time chat:
- Each ticket gets a `conversationId` linking to the chat system
- Users can chat with support team through the conversation
- Unread message counts are available in ticket responses
- Messages are tracked separately in the message system

## Best Practices

1. **Pagination**: Always implement pagination for ticket lists
2. **Real-time Updates**: Consider WebSocket integration for live status updates
3. **File Uploads**: Implement secure file upload for attachments
4. **Search**: Implement debounced search to avoid excessive API calls
5. **Caching**: Cache ticket lists and implement smart refresh strategies
6. **Offline Support**: Consider offline capabilities for viewing cached tickets

## Security Considerations

- Users can only access their own tickets (unless admin)
- Guest tickets require email verification
- File uploads should be validated and scanned
- Rate limiting should be implemented for ticket creation
- Sensitive information should be redacted in logs
