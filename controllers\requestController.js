import Request from "../models/requestModel.js";
import asyncHand<PERSON> from "express-async-handler";
import AppError from "./../utils/appError.js";

export const createNewRequest = asyncHandler(async (req, res) => {
  const { title, region, category, language, additionalInformation } = req.body;
  // Validate required fields
  if (!title || !region || !language) {
    throw new AppError(
      "Title, region, category, and language are required fields.",
      400
    );
  }

  const newRequest = await Request.create({
    title,
    region,
    category,
    language,
    additionalInformation,
    // isApproved: false, // Default to false
    seller: req.user._id, // Assuming sellerProtect adds the seller's info to req.user
  });

  res.status(201).json({
    success: true,
    message: "Request created successfully.",
    request: newRequest,
  });
});
// @desc    Get a single request by ID
// @route   GET /api/requests/:id
// @access  Authenticated Users
export const getRequestById = asyncHandler(async (req, res) => {
  const request = await Request.findById(req.params.id).populate(
    "seller",
    "name email"
  );
  if (!request) {
    throw new AppError("Request not found.", 404);
  }

  res.status(200).json({
    success: true,
    request,
  });
});
// @desc    Get all requests
// @route   GET /api/requests
// @access  Admin
export const getAllRequests = asyncHandler(async (req, res) => {
  try {
    let requests;

    // Check the user's role
    if (req.user.role === "seller") {
      // If the user is a seller, filter requests by their ID
      requests = await Request.find({ seller: req.user._id });
    } else if (req.user.role === "admin") {
      // If the user is an admin, return all requests
      requests = await Request.find({})
        .populate("seller", "name email")
        .populate("category", "categoryName");
    } else {
      // Handle unauthorized access or other roles
      return res.status(403).json({
        success: false,
        message: "Unauthorized to access requests",
      });
    }

    res.status(200).json({
      success: true,
      total: requests.length,
      requests,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Server Error",
      error: error.message,
    });
  }
});

// @desc    Approve a request
// @route   PUT /api/requests/:id/approve
// @access  Admin
export const approveRequest = asyncHandler(async (req, res) => {
  const request = await Request.findById(req.params.id);

  if (!request) {
    throw new AppError("Request not found.", 404);
  }

  if (request.isApproved === "approved") {
    throw new AppError("Request is already approved.", 400);
  }

  request.isApproved = "approved";
  await request.save();

  res.status(200).json({
    success: true,
    message: "Request approved successfully.",
    request,
  });
});
export const rejectRequest = asyncHandler(async (req, res) => {
  const request = await Request.findById(req.params.id);

  if (!request) {
    throw new AppError("Request not found.", 404);
  }

  if (request.isApproved === "rejected") {
    throw new AppError("Request is already approved.", 400);
  }

  request.isApproved = "rejected";
  await request.save();

  res.status(200).json({
    success: true,
    message: "Request approved successfully.",
    request,
  });
});
// @desc    Delete a request
// @route   DELETE /api/requests/:id
// @access  Admin
export const deleteRequest = asyncHandler(async (req, res) => {
  const request = await Request.findById(req.params.id);

  if (!request) {
    throw new AppError("Request not found.", 404);
  }

  await request.remove();

  res.status(200).json({
    success: true,
    message: "Request deleted successfully.",
  });
});

// @desc    Migrate requests (example: for bulk updates or transfers)
// @route   GET /api/requests/migrate
// @access  Admin
export const migrateRequests = asyncHandler(async (req, res) => {
  // Example migration logic: Set all requests with a missing `isApproved` field to `false`
  const updatedRequests = await Request.updateMany(
    { isApproved: { $exists: false } },
    { $set: { isApproved: false } }
  );

  res.status(200).json({
    success: true,
    message: `${updatedRequests.nModified} requests were updated during migration.`,
  });
});
