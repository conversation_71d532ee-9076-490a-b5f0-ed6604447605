import mongoose from "mongoose";
import slugify from "slugify";

const blogSchema = new mongoose.Schema(
  {
    title: { type: String, required: true },
    slug: { type: String, unique: true },
    shortSummary: { type: String },
    summary: { type: String },
    metaTags: { type: [String] },
    keywords: { type: [String] },
    image: { type: String },
    language: { type: String, required: true },
    tags: { type: [String] },
    category: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "BlogCategory",
      required: true,
    },
    details: {
      description: { type: String }, // Default language description (Rich text)
      germanDetails: {
        title: { type: String },
        shortSummary: { type: String },
        summary: { type: String },
        tags: { type: [String] },
        keywords: { type: [String] },
        description: {
          type: String,
        },
      },
      frenchDetails: {
        title: { type: String },
        shortSummary: { type: String },
        summary: { type: String },
        tags: { type: [String] },
        keywords: { type: [String] },
        description: {
          type: String,
        },
      },
      spanishDetails: {
        title: { type: String },
        shortSummary: { type: String },
        summary: { type: String },
        tags: { type: [String] },
        keywords: { type: [String] },
        description: {
          type: String,
        },
      },
      italianDetails: {
        title: { type: String },
        shortSummary: { type: String },
        summary: { type: String },
        tags: { type: [String] },
        keywords: { type: [String] },
        description: {
          type: String,
        },
      },
    },
  },
  { timestamps: true }
);

// Auto-generate slug before saving
blogSchema.pre("save", function (next) {
  if (!this.slug) {
    this.slug = slugify(this.title, { lower: true, strict: true });
  }
  next();
});

const Blog = mongoose.model("Blog", blogSchema);
export default Blog;
