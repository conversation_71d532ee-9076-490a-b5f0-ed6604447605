import asyncHandler from "express-async-handler";
import {pusher} from "../services/notificationService.js";

export const pusherAuth = asyncHandler(async (req, res) => {

    const socketId = req.body.socket_id;
    const channel = req.body.channel_name;

    const userId = req.user._id;

    // Check that this user is allowed to join this channel
    if (channel === `private-user-${userId}`) {
        const auth = pusher.authorizeChannel(socketId, channel);
        res.send(auth);
    } else {
        res.status(403).send('Unauthorized');
    }
});