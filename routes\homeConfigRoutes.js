// homepageConfigRoutes.js
import express from "express";
import {
  createHomepageConfig,
  getHomepageConfig,
  updateHomepageConfig,
  deleteHomepageConfig,
} from "../controllers/homepageConfigController.js";
import { protect, admin } from "../middleware/authMiddleware.js";

const router = express.Router();

// Create a new homepage configuration (admin only)
router.route("/").post(protect, admin, createHomepageConfig);

// Get, update, or delete a homepage configuration by ID (admin only)
router
  .route("/:id")
  .get(getHomepageConfig)
  .patch(protect, admin, updateHomepageConfig)
  .delete(protect, admin, deleteHomepageConfig);

export default router;
