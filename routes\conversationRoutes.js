import express from "express";
import {
  createConversation,
  getConversations,
  getConversation,
} from "./../controllers/conversationController.js";
import { protect } from "../middleware/authMiddleware.js";

const router = express.Router();

// Create new conversation
router.post("/", protect, createConversation);
// Get all user conversations
router.get("/", protect, getConversations);
router.get("/:id", protect, getConversation);

// Get single conversation

// Archive conversation
// router.patch("/:id/archive", protect, archiveConversation);

export default router;
