// controllers/blogCategoryController.js
import * as factory from "./handlerFactory.js";
import BlogCategory from "../models/blogCategoryModel.js";

export const createBlogCategory = factory.createOne(BlogCategory);
export const getBlogCategory = factory.getOne(BlogCategory);
export const updateBlogCategory = factory.updateOne(BlogCategory);
export const deleteBlogCategory = factory.deleteOne(BlogCategory);
export const getAllBlogCategories = factory.getAll("", BlogCategory);
