import mongoose from "mongoose";

const conversationSchema = new mongoose.Schema(
  {
    offer: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Offer"
    },
    client: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    seller: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    lastMessage: String,
    unreadClient: { type: Number, default: 0 },
    unreadSeller: { type: Number, default: 0 },
    archived: { type: Boolean, default: false },
    type: { type: String, required: true, enums: ["offer", "ticket"]},
  },
  { timestamps: true }
);

conversationSchema.index({ client: 1, seller: 1, offer: 1 });

const Conversation = mongoose.model("Conversation", conversationSchema);
export default Conversation;
