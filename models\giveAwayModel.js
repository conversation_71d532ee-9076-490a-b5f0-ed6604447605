// models/Giveaway.js
import mongoose from 'mongoose';

const ActionSchema = new mongoose.Schema({
    type: { type: String, required: true }, // e.g., follow_facebook, follow_tiktok
    label: { type: String, required: true }, // e.g., "Follow on Facebook"
    points: { type: Number, default: 10 },
    url: { type: String }, // e.g., link to the FB page
});

const GiveawaySchema = new mongoose.Schema({
    title: { type: String, required: true },
    description: String,
    startDate: { type: Date, required: true },
    endDate: { type: Date, required: true },
    prizes: [String],
    actions: [ActionSchema], // actions a user can take
}, { timestamps: true });

export default mongoose.model('Giveaway', GiveawaySchema);
