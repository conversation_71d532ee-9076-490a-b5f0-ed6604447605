import sendgrid from "@sendgrid/mail";
import User from "../../models/userModel.js";

export const inactiveUser = async ({subject, html}) => {

    const threshold = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)

    const inactiveUsers = await User.find({
        lastActiveAt: { $lt: threshold },
      });

    const messages = inactiveUsers.map(sub => ({
        to: sub.email,
        from: process.env.EMAIL_FROM,
        subject,
        html
    }));

    // You can use `sendMultiple` (SendGrid supports batching)
    await sendgrid.send(messages);
};
