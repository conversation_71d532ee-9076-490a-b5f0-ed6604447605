# Get All Orders API Documentation

## Overview
This API endpoint provides comprehensive order management functionality for your frontend application. It includes advanced filtering, pagination, sorting, and statistical data.

## Endpoint Details

### Production Endpoint (Admin Only)
```
GET /order/all-orders
```
**Authentication:** Required (<PERSON><PERSON>)
**Authorization:** Admin role required

### Test Endpoint (For Development)
```
GET /order/all-orders-test
```
**Authentication:** Required (<PERSON>er <PERSON>)
**Authorization:** Any authenticated user

## Request Parameters

### Pagination
- `page` (number, default: 1) - Page number
- `limit` (number, default: 10, max: 100) - Items per page

### Filtering
- `status` (string) - Filter by order status
  - Values: `pending`, `review`, `completed`, `refund`, `cancelled`, `delivered`
- `orderNumber` (string) - Search by order number (partial match)
- `orderIncrementId` (string) - Search by order increment ID (partial match)
- `reservationId` (string) - Search by reservation ID (partial match)
- `userId` (string) - Filter by specific user ID
- `sellerId` (string) - Filter by specific seller ID
- `createdDateFrom` (string, ISO date) - Filter orders created after this date
- `createdDateTo` (string, ISO date) - Filter orders created before this date
- `minTotal` (number) - Filter orders with total amount greater than or equal to this value
- `maxTotal` (number) - Filter orders with total amount less than or equal to this value
- `search` (string) - Global search across multiple fields

### Sorting
- `sortBy` (string, default: 'createdAt') - Field to sort by
  - Values: `createdAt`, `updatedAt`, `total`, `orderNumber`, `status`
- `sortOrder` (string, default: 'desc') - Sort direction
  - Values: `asc`, `desc`

## Example Requests

### Basic Request
```bash
curl -X GET "http://localhost:4000/order/all-orders?page=1&limit=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Advanced Filtering
```bash
curl -X GET "http://localhost:4000/order/all-orders?status=completed&createdDateFrom=2024-01-01&minTotal=50&sortBy=total&sortOrder=desc" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Search Example
```bash
curl -X GET "http://localhost:4000/order/all-orders?search=john&page=1&limit=20" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Response Format

```json
{
  "success": true,
  "data": {
    "orders": [
      {
        "_id": "order_id",
        "orderNumber": "#123",
        "orderIncrementId": "ABC123DEF",
        "reservationId": "uuid-string",
        "user": "user_id",
        "userDetails": {
          "_id": "user_id",
          "firstName": "John",
          "lastName": "Doe",
          "email": "<EMAIL>",
          "role": "user",
          "avatar": "avatar_url"
        },
        "items": [
          {
            "_id": "item_id",
            "offer": "offer_id",
            "quantity": 1,
            "price": 29.99,
            "status": "completed",
            "keys": ["license-key-1"],
            "offerDetails": {
              "_id": "offer_id",
              "name": "Product Name",
              "category": "Software",
              "subcategory": "Games",
              "region": "Global",
              "expectedPrice": 25.00,
              "customerPays": 29.99,
              "instantDelivery": true,
              "active": true,
              "templateDetails": {
                "templateName": "Product Template",
                "coverImage": "image_url",
                "category": "Software"
              },
              "sellerDetails": {
                "_id": "seller_id",
                "firstName": "Jane",
                "lastName": "Smith",
                "email": "<EMAIL>",
                "avatar": "seller_avatar_url"
              }
            }
          }
        ],
        "subtotal": 29.99,
        "serviceFee": 2.99,
        "total": 32.98,
        "savings": 5.00,
        "status": "completed",
        "latest": true,
        "releaseDate": "2024-01-15T10:00:00.000Z",
        "paymentData": {
          "paymentId": "payment_id",
          "paymentOrderId": "payment_order_id",
          "transactionId": "transaction_id",
          "amount": 32.98,
          "vatAmount": 5.28,
          "captureUrl": "capture_url",
          "status": "captured",
          "userId": "user_id"
        },
        "createdAt": "2024-01-15T09:30:00.000Z",
        "updatedAt": "2024-01-15T10:00:00.000Z",
        "totalItems": 1,
        "customerName": "John Doe",
        "uniqueSellers": 1
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 5,
      "totalOrders": 50,
      "hasNextPage": true,
      "hasPrevPage": false,
      "limit": 10
    },
    "statistics": {
      "totalRevenue": 1649.50,
      "averageOrderValue": 32.99,
      "statusBreakdown": {
        "completed": 35,
        "pending": 8,
        "review": 4,
        "cancelled": 2,
        "refund": 1
      }
    },
    "filters": {
      "status": null,
      "orderNumber": null,
      "orderIncrementId": null,
      "reservationId": null,
      "userId": null,
      "sellerId": null,
      "createdDateFrom": null,
      "createdDateTo": null,
      "minTotal": null,
      "maxTotal": null,
      "search": null
    },
    "sorting": {
      "sortBy": "createdAt",
      "sortOrder": "desc"
    }
  }
}
```

## Error Responses

### Authentication Error
```json
{
  "status": "error",
  "message": "Not authorized, no token"
}
```

### Authorization Error
```json
{
  "status": "fail",
  "message": "Only Admin can access this route"
}
```

### Server Error
```json
{
  "success": false,
  "message": "Error fetching all orders",
  "error": "Detailed error message"
}
```

## Frontend Integration Examples

### React/JavaScript Example
```javascript
const fetchAllOrders = async (filters = {}) => {
  try {
    const queryParams = new URLSearchParams(filters).toString();
    const response = await fetch(`/order/all-orders?${queryParams}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      }
    });
    
    const data = await response.json();
    
    if (data.success) {
      return data.data;
    } else {
      throw new Error(data.message);
    }
  } catch (error) {
    console.error('Error fetching orders:', error);
    throw error;
  }
};

// Usage examples
const ordersData = await fetchAllOrders({
  page: 1,
  limit: 20,
  status: 'completed',
  sortBy: 'createdAt',
  sortOrder: 'desc'
});
```

### Vue.js Example
```javascript
// In your Vue component
export default {
  data() {
    return {
      orders: [],
      pagination: {},
      statistics: {},
      loading: false,
      filters: {
        page: 1,
        limit: 10,
        status: '',
        search: ''
      }
    }
  },
  methods: {
    async fetchOrders() {
      this.loading = true;
      try {
        const response = await this.$http.get('/order/all-orders', {
          params: this.filters,
          headers: {
            'Authorization': `Bearer ${this.$store.state.auth.token}`
          }
        });
        
        const { orders, pagination, statistics } = response.data.data;
        this.orders = orders;
        this.pagination = pagination;
        this.statistics = statistics;
      } catch (error) {
        console.error('Error fetching orders:', error);
      } finally {
        this.loading = false;
      }
    }
  }
}
```

## Features

### 🔍 Advanced Search
- Search across order numbers, customer names, emails, product names
- Case-insensitive partial matching

### 📊 Rich Statistics
- Total revenue calculation
- Average order value
- Status breakdown with counts

### 🎯 Flexible Filtering
- Filter by order status, date ranges, amounts
- User and seller specific filtering

### 📄 Smart Pagination
- Configurable page size (max 100 items)
- Navigation helpers (hasNextPage, hasPrevPage)

### 🔄 Sorting Options
- Sort by any field (date, amount, status, etc.)
- Ascending/descending order

### 💾 Complete Order Data
- Full customer information
- Detailed product/offer information
- Seller details for each item
- Payment information
- License keys and delivery status

## Performance Notes

- Maximum 100 items per page to prevent performance issues
- Efficient MongoDB aggregation pipeline
- Indexed fields for optimal query performance
- Minimal data transfer with selective field projection

## Security

- JWT authentication required
- Admin role verification for production endpoint
- Input validation and sanitization
- Protected against injection attacks
