// models/EUTax.js
import mongoose from 'mongoose';

const EUTaxSchema = new mongoose.Schema({
    country: {
        type: String,
        required: true,
        unique: true,
        trim: true,
    },
    taxPercentage: {
        type: Number,
        required: true,
        min: 0,
        max: 100,
    },
}, {
    timestamps: true,
});

export default mongoose.models.EUTax || mongoose.model('EUTax', EUTaxSchema);