import express from "express";

import {getAllTax, patchTax, postTaxes, getTaxByCountry} from "./../controllers/taxController.js";
import {protect} from "../middleware/authMiddleware.js";

const router = express.Router();

router.route("/").get(protect, getAllTax)
    .post(protect, postTaxes)

router.patch("/:id", protect, patchTax);
router.get("/:country", protect, getTaxByCountry);

export default router;
