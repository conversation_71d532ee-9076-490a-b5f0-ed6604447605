import HelpCategory from "../models/helpCategoryModel.js";

// ✅ Get all predefined categories
export const getAllCategories = async (req, res) => {
  try {
    const categories = await HelpCategory.find();
    res.status(200).json(categories);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// ✅ Get a single category by ID
export const getCategory = async (req, res) => {
  try {
    const category = await HelpCategory.findById(req.params.categoryId);
    if (!category)
      return res.status(404).json({ message: "Category not found" });
    res.status(200).json(category);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// ✅ Add a subcategory to a predefined category
export const addSubcategory = async (req, res) => {
  try {
    const { title } = req.body;
    const category = await HelpCategory.findById(req.params.categoryId);
    if (!category)
      return res.status(404).json({ message: "Category not found" });

    const newSubcategory = { title, questions: [] };
    category.subcategories.push(newSubcategory);
    await category.save();
    res.status(201).json(category);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};

// ✅ Update a subcategory
export const updateSubcategory = async (req, res) => {
  try {
    const { title, isActive } = req.body; // Include isActive in the request payload
    const category = await HelpCategory.findById(req.params.categoryId);
    if (!category)
      return res.status(404).json({ message: "Category not found" });

    const subcategory = category.subcategories.id(req.params.subcategoryId);
    if (!subcategory)
      return res.status(404).json({ message: "Subcategory not found" });

    // Update fields only if they are provided in the request
    if (title !== undefined) subcategory.title = title;
    if (isActive !== undefined) subcategory.isActive = isActive;

    await category.save();
    res
      .status(200)
      .json({ message: "Subcategory updated successfully", category });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

export const getSubCategory = async (req, res) => {
  try {
    const { categoryId, subcategoryId } = req.params;
    const category = await HelpCategory.findById(categoryId);

    const subcategory = category.subcategories.find(sub => sub._id.toString() === subcategoryId);

    if (!subcategory) return res.status(404).json({ message: "Subcategory not found" });

    return res.status(200).json({
      subcategoryTitle: subcategory.title,
      questions: subcategory.questions,
    });
  } catch (error) {
    console.error("Error fetching subcategory:", error);
    return res.status(500).json({ message: "Internal Server Error" });
  }
};


// ✅ Delete a subcategory
export const deleteSubcategory = async (req, res) => {
  try {
    const category = await HelpCategory.findById(req.params.categoryId);
    if (!category)
      return res.status(404).json({ message: "Category not found" });

    category.subcategories = category.subcategories.filter(
      (sub) => sub._id.toString() !== req.params.subcategoryId
    );

    await category.save();
    res.status(200).json({ message: "Subcategory deleted successfully" });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// ✅ Add a question to a subcategory
export const addQuestion = async (req, res) => {
  try {
    const { title, content } = req.body;
    const category = await HelpCategory.findById(req.params.categoryId);
    if (!category)
      return res.status(404).json({ message: "Category not found" });

    const subcategory = category.subcategories.id(req.params.subcategoryId);
    if (!subcategory)
      return res.status(404).json({ message: "Subcategory not found" });

    subcategory.questions.push({ title, content });
    await category.save();
    res.status(201).json(category);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
};

// ✅ Update a question
export const updateQuestion = async (req, res) => {
  try {
    const { title, content } = req.body;
    const category = await HelpCategory.findById(req.params.categoryId);
    if (!category)
      return res.status(404).json({ message: "Category not found" });

    const subcategory = category.subcategories.id(req.params.subcategoryId);
    if (!subcategory)
      return res.status(404).json({ message: "Subcategory not found" });

    const question = subcategory.questions.id(req.params.questionId);
    if (!question)
      return res.status(404).json({ message: "Question not found" });

    question.title = title;
    question.content = content;
    await category.save();
    res.status(200).json(category);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

// ✅ Delete a question
export const deleteQuestion = async (req, res) => {
  try {
    const category = await HelpCategory.findById(req.params.categoryId);
    if (!category)
      return res.status(404).json({ message: "Category not found" });

    const subcategory = category.subcategories.id(req.params.subcategoryId);
    if (!subcategory)
      return res.status(404).json({ message: "Subcategory not found" });

    subcategory.questions = subcategory.questions.filter(
      (q) => q._id.toString() !== req.params.questionId
    );

    await category.save();
    res.status(200).json({ message: "Question deleted successfully" });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
};

export const setupCategories = async () => {
  try {
    const predefinedCategories = [
      { title: "Accounts", subcategories: [] },
      { title: "Vendors", subcategories: [] },
      { title: "Orders & Returns", subcategories: [] },
      { title: "Shipping & Delivery", subcategories: [] },
      { title: "Earnings & Payouts", subcategories: [] },
      { title: "payment", subcategories: [] },
    ];
    // Clear existing categories to avoid duplicates
    await HelpCategory.deleteMany({});
    await HelpCategory.insertMany(predefinedCategories);
    // res.status(201).json({ message: "Categories initialized successfully" });
  } catch (error) {
    console.log("Categories initialization failed");
  }
};

// setupCategories();
