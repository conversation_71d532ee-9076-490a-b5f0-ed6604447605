import express from 'express';
import { protect, admin } from './../middleware/authMiddleware.js';
import {
    createTemplate,
    getAllTemplates,
    getTemplateById,
    updateTemplate,
    deleteTemplate,
} from './../controllers/templateController.js';

const router = express.Router();

// Route to create a new template (admin only)
router.route('/')
    .post(protect, admin, createTemplate);

// Route to get all templates (seller/admin)
router.route('/')
    .get(protect, getAllTemplates);

// Route to get a template by ID (seller/admin)
router.route('/:id')
    .get(protect, getTemplateById);

// Route to update a template by ID (admin only)
router.route('/:id')
    .patch(protect, admin, updateTemplate);

// Route to delete a template by ID (admin only)
router.route('/:id')
    .delete(protect, admin, deleteTemplate);

export default router;
