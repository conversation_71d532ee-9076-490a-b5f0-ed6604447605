import express from "express";
import {
  createShop,
  getAllShops,
  getShopById,
  updateUserAndShop,
  deleteShop,
  getSellerShop,
  addFollower,
  removeFollower,
  toggleFollow,
  checkFollowStatus,
  dashboard,
  getBestSellingOffers,
} from "../controllers/shopController.js";
import { protect, admin } from "../middleware/authMiddleware.js";

const router = express.Router();
// Public routes
router.route("/").get(getAllShops);
router.get("/my-shop", protect, getSellerShop);
router.route("/:id").get(getShopById);
router.route("/:shopId/follow").post(addFollower);
router.route("/:shopId/unfollow").post(removeFollower);
router.route("/dashboard/:sellerId").get(dashboard);
router.get("/seller/:sellerId/best-selling", getBestSellingOffers);
// Protected routes (only authenticated users can create, only admin can update/delete)
router.use(protect);
router.route("/").post(createShop);
router
  .route("/:id")
  .patch(protect, updateUserAndShop)
  .delete(admin, deleteShop);
router.route("/:shopId/follow-toggle").post(toggleFollow);
router.route("/:shopId/follow-status").post(checkFollowStatus);

export default router;
