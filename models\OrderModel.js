import mongoose from "mongoose";
import crypto from "crypto";
import { v4 as uuidv4 } from "uuid";

const counterSchema = new mongoose.Schema({
  name: { type: String, required: true, unique: true },
  value: { type: Number, default: 0 }, // Starting order number
});
const Counter = mongoose.model("Counter", counterSchema);

const paymentDataSchema = new mongoose.Schema({
  paymentId: { type: String },
  paymentOrderId: { type: String },
  transactionId: { type: String },
  amount: { type: Number },
  vatAmount: { type: Number },
  captureUrl: { type: String },
  status: { type: String, enum: ['pending', 'captured', 'failed'], default: 'pending' },
  userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' }
}, { _id: false });

const orderSchema = new mongoose.Schema(
  {
    orderNumber: {
      type: String,
      unique: true,
    },
    orderIncrementId: {
      type: String,
      unique: true,
    },
    reservationId: {
      type: String,
      unique: true,
    },
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    items: [
      {
        offer: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "Offer",
          required: true,
        },
        quantity: {
          type: Number,
          required: true,
          min: 1,
        },
        price: {
          type: Number,
          required: true,
        },
        status: {
          type: String,
          enum: [
            "processing",
            "review",
            "completed",
            "refund",
            "cancelled",
            "delivered",
          ],
          default: "processing",
        },
        keys: [
          {
            type: String,
            required: true,
          },
        ],
      },
    ],
    subtotal: {
      type: Number,
      required: true,
    },
    serviceFee: {
      type: Number,
      required: true,
    },
    total: {
      type: Number,
      required: true,
    },
    savings: {
      type: Number,
      required: true,
    },
    status: {
      type: String,
      enum: [
        "pending",
        "review",
        "completed",
        "refund",
        "cancelled",
        "delivered",
      ],
      default: "completed",
    },
    latest: {
      type: Boolean,
      default: true,
    },
    releaseDate: {
      type: Date,
    },
    paymentData: {
      type: paymentDataSchema,
      default: null
    }
  },
  { timestamps: true },
);
// Generate unique orderIncrementId & reservationId before saving
orderSchema.pre("save", async function (next) {
  try {
    if (!this.orderIncrementId) {
      this.orderIncrementId = crypto
        .randomBytes(6)
        .toString("base64")
        .replace(/[^a-zA-Z0-9]/g, "")
        .substring(0, 10);
    }
    if (!this.reservationId) {
      this.reservationId = uuidv4();
    }
    if (!this.orderNumber) {
      let counter = await Counter.findOne({ name: "orderNumber" });

      // If counter does not exist, create it with the default value
      if (!counter) {
        counter = await new Counter({
          name: "orderNumber",
          value: 0,
        }).save();
      }

      // Increment the order number and update counter
      counter = await Counter.findOneAndUpdate(
        { name: "orderNumber" },
        { $inc: { value: 1 } },
        { new: true },
      );

      // Assign the new order number
      this.orderNumber = `#${counter.value}`;
    }
    next();
  } catch (error) {
    next(error);
  }
});
const Order = mongoose.model("Order", orderSchema);

export default Order;
