import User from "../models/userModel.js";
import Ticket from "../models/ticketModel.js";
import Conversation from "../models/conversationModel.js";

export async function addConversation(doc) {

    const admin = await User.findOne({ role: 'admin' });
    const currentUser = await User.findOne({ name: doc.name });

    const conversation = new Conversation({
        offer: null,
        client: currentUser._id,
        seller: admin._id,
        type: 'ticket'
    });

    await conversation.save();

    const ticket = await Ticket.findOne({ _id: doc._id });
    ticket.conversationId = conversation._id;
    await ticket.save();
}


const ticketTimers = new Map();
export async function resetTicketTimer({ticketId, lastMessageTime}) {
    if (ticketTimers.has(ticketId)) {
        clearTimeout(ticketTimers.get(ticketId));
    }

    const now = new Date();
    const delay = 24 * 60 * 60 * 1000 - (now.getTime() - lastMessageTime.getTime());

    const safeDelay = Math.max(delay, 0);

    const timeout = setTimeout(() => {
        Ticket.findByIdAndUpdate(
            ticketId,
            { status: 'closed' },
            {new : true}
        );

        ticketTimers.delete(ticketId);

    }, safeDelay);

    ticketTimers.set(ticketId, timeout);
}