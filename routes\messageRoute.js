import express from "express";
import {
  sendMessage,
  getMessages,
  markAsRead,
} from "../controllers/messageController.js";
import { protect } from "../middleware/authMiddleware.js";

const router = express.Router();

// Send message
router.post("/", protect, sendMessage);

// Get messages
router.get("/:conversationId", protect, getMessages);

// Mark messages as read
router.patch("/read", protect, markAsRead);

export default router;
