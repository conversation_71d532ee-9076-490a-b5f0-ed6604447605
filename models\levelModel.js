import mongoose from "mongoose";

const LevelSchema = new mongoose.Schema({
  name: { type: String, required: true },
  order: { type: Number, required: true },
  fromOrders: { type: Number, required: true },
  toOrders: { type: Number }, // Optional, can be undefined/null for infinite
  earningReleaseDays: { type: Number, required: true },
  commissionPercent: { type: Number, required: true },
}, { timestamps: true });

LevelSchema.pre("save", async function (next) {
  const Level = mongoose.model("Level");
  // Find all other levels
  const levels = await Level.find({ _id: { $ne: this._id } });
  for (const lvl of levels) {
    const existingFrom = lvl.fromOrders;
    const existingTo = lvl.toOrders;
    // If either toOrders is undefined/null, treat as infinite
    const thisTo = this.toOrders == null ? Infinity : this.toOrders;
    const otherTo = existingTo == null ? Infinity : existingTo;
    // Check for overlap
    if (
      (this.fromOrders <= otherTo) && (thisTo >= existingFrom)
    ) {
      return next(new Error("Order range overlaps with an existing level."));
    }
  }
  next();
});

const Level = mongoose.model("Level", LevelSchema);
export default Level; 