import asyncHandler from "express-async-handler";
import User from "../models/userModel.js";
import Cart from "../models/cartModel.js";
import Order from "./../models/OrderModel.js";
import {notifyUser} from "../services/notificationService.js";
import {orderCompletionMail} from "../utils/emailTemplates/templates.js";
import sendgrid from "@sendgrid/mail";
import Offer from "../models/offerModel.js";

// swedbank pay
export const swedbankPay = asyncHandler(async (req, res) => {
  const { amount, urls } = req.body;

  const userId = req.user._id;

  const intAmount = amount * 100;

  try {
    const response = await fetch(
      "https://api.externalintegration.payex.com/psp/paymentorders",
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${process.env.SWEDBANK_API_TOKEN}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          paymentorder: {
            operation: "Purchase",
            currency: "USD",
            amount: intAmount,
            vatAmount: 0,
            description: "Test Payment Order",
            userAgent: req.headers["user-agent"],
            language: "en-US",
            urls,
            payeeInfo: {
              payeeId: process.env.SWEDBANK_API_PAYEE,
              payeeReference: `ref${Date.now()}`,
              payeeName: "Vbrae",
              orderReference: "Order1100",
            },
            metadata: {
              userId,
            },
          },
        }),
      },
    );

    if (!response.ok) {
      const error = new Error(`Swedbank Pay API Error: `);
      throw error;
    }

    const responseData = await response.json();

    const viewCheckoutOperation = responseData.operations.find(
      (op) => op.rel === "redirect-paymentorder",
    );

    const paymentUrl = responseData.operations.find(
      (op) => op.rel === "view-paymentsession",
    );

    if (viewCheckoutOperation?.href) {
      res.status(201).json({
        message: "Payment order created successfully.",
        paymentOrderId: responseData.paymentOrder.id,
        viewCheckoutUrl: viewCheckoutOperation.href,
        paymentUrl: paymentUrl.href,
        ...responseData
      });
    } else {
      res.status(500).json({ error: "view-checkout URL not received." });
    }
  } catch (error) {
    console.error("Swedbank Pay Error:", error);
    res.status(500).json({ error: error.message });
  }
});

export const paymentCallback = asyncHandler(async (req, res) => {
  try {
    const data = req.body;
    const paymentId = data.payment?.id;
    const paymentOrderId = data.paymentOrder?.id;
    const transactionId = data.transaction?.id;
    const orderReference = data.orderReference;

    if (!paymentId || !paymentOrderId || !transactionId) {
      return res.status(400).json({ error: "Missing required payment identifiers" });
    }

    // Detect if this is a capture callback by examining the transaction path
    const isCaptureCallback = transactionId.includes('/captures/');
    const paymentRes = await fetch(
      `https://api.externalintegration.payex.com${paymentId}`,
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${process.env.SWEDBANK_API_TOKEN}`,
          "Content-Type": "application/json",
        },
      }
    );

    if (!paymentRes.ok) {
      console.error("Failed to fetch payment details:", await paymentRes.text());
      return res.status(500).json({ error: "Failed to retrieve payment details" });
    }

    const paymentData = await paymentRes.json();
    const metadataEndpoint = paymentData.payment?.metadata?.id;

    // Step 1: Handle capture callback
    if (isCaptureCallback) {
      console.log('Capture callback received for order:', orderReference);
    }

    // Step 2: Handle initial authorization callback
    console.log('Initial payment authorization callback received');

    const status = paymentData.payment?.state;

    // Step 3: Only attempt capture if payment is in 'Ready' state
    if (status === "Ready") {
      const captureOperation = paymentData.operations.find(op => op.rel === "create-capture");

      if (!captureOperation?.href) {
        console.error("Capture operation not available");
        return res.status(400).json({ error: "Capture operation not available" });
      }
        // Get user ID from metadata
        const metadataRes = await fetch(
          `https://api.externalintegration.payex.com${metadataEndpoint}`,
          {
            method: "GET",
            headers: {
              Authorization: `Bearer ${process.env.SWEDBANK_API_TOKEN}`,
              "Content-Type": "application/json",
            },
          }
        );

        if (!metadataRes.ok) {
          console.error("Failed to fetch metadata:", await metadataRes.text());
          return res.status(500).json({ error: "Failed to retrieve payment metadata" });
        }

        const { metadata } = await metadataRes.json();
        const payerReference = metadata.userId;

        // Save payment data to order for later capture
        return await processOrderCreation({payerReference, paymentOrderId, res, paymentId, transactionId, captureOperation});
      }

      // Handle other statuses
      console.log("Payment not in Ready state:", status);
      return res.status(400).json({ error: `Payment not ready for capture. Current state: ${status}` });

    } catch (error) {
      console.error("Payment callback processing error:", error);
      res.status(500).json({ error: "Callback processing failed" });
    }
  });

// Helper function to process order fulfillment
async function processOrderCreation({
                                      payerReference,
                                      paymentOrderId,
                                      res,
                                      paymentId,
                                      transactionId,
                                      captureOperation
                                    }) {
  try {
    // Step 0: Validate input
    if (!paymentOrderId) {
      return res.status(400).json({ error: "Missing payment order ID" });
    }

    // Step 1: Fetch user and cart
    const [user, cart] = await Promise.all([
      User.findById(payerReference),
      Cart.findOne({ user: payerReference }).populate({
        path: "items.offer",
        model: "Offer",
        select: "customerPays expectedPrice licenseKeys stock sold instantDelivery usedKeys"
      })
    ]);

    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }

    if (!cart || cart.items.length === 0) {
      return res.status(404).json({ error: "Cart not found or empty" });
    }

    // Step 2: Calculate totals
    const { subtotal, savings, orderItems } = cart.items.reduce(
        (acc, item) => {
          const offer = item.offer;
          const itemTotal = offer.customerPays * item.quantity;
          const itemSavings =
              (offer.expectedPrice - offer.customerPays) * item.quantity;

          acc.subtotal += itemTotal;
          acc.savings += itemSavings;
          acc.orderItems.push({
            offer: offer._id,
            quantity: item.quantity,
            price: offer.customerPays,
            itemTotal
          });

          return acc;
        },
        { subtotal: 0, savings: 0, orderItems: [] }
    );

    const serviceFee = parseFloat((subtotal * 0.1).toFixed(2));
    const total = parseFloat((subtotal + serviceFee).toFixed(2));

    // Mark previous orders as not latest
    await Order.updateMany(
        { user: payerReference, latest: true },
        { $set: { latest: false } }
    );

    // Step 3: Create order
    const order = await Order.create({
      user: payerReference,
      items: orderItems,
      status: user.isKycVerified === "approved" ? "completed" : "review",
      subtotal,
      serviceFee,
      total,
      savings,
      latest: true,
      paymentOrderId,
      createdAt: new Date(),
      paymentData: {
        paymentId,
        paymentOrderId,
        transactionId,
        vatAmount: 0,
        captureUrl: captureOperation.href,
        status: 'pending',
      }
    });

    // Step 4: Clear cart
    await Cart.deleteOne({ user: payerReference });

    // Step 5: If user is KYC verified, deliver instantly
    if (user.isKycVerified === "approved") {
      for (const item of order.items) {
        const offer = await Offer.findById(item.offer);

        if (!offer?.instantDelivery) continue;

        if (offer.licenseKeys.length < item.quantity) {
          console.error(`Insufficient keys for offer ${offer._id}`);
          continue;
        }

        const keysToTransfer = offer.licenseKeys.splice(0, item.quantity);
        item.keys = keysToTransfer;

        offer.usedKeys.push(
            ...keysToTransfer.map((key) => ({
              key,
              orderId: order._id,
            }))
        );

        offer.stock = offer.licenseKeys.length;
        offer.sold += item.quantity;
        await offer.save();

        item.status = "completed";
      }

      // Capture payment
      if (order.paymentData?.status === "pending") {
        try {
          const captureRes = await fetch(order.paymentData.captureUrl, {
            method: "POST",
            headers: {
              Authorization: `Bearer ${process.env.SWEDBANK_API_TOKEN}`,
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              transaction: {
                amount: order.total * 100,
                vatAmount: order.paymentData.vatAmount || 0,
                description: `Capture for order ${order._id}`,
                payeeReference: `capture${Date.now()}`,
              },
            }),
          });

          if (!captureRes.ok) {
            console.error("Payment capture failed:", await captureRes.text());
            order.paymentData.status = "failed";
          } else {
            order.paymentData.status = "captured";
          }
        } catch (err) {
          console.error("Error capturing payment:", err);
          order.paymentData.status = "failed";
        }
      }
      order.status = "completed";
    }

    await order.save();

    // Step 6: Send order completion email if completed
    if (order.status === "completed") {
      try {
        const userDetails = await User.findById(order.user);
        const mailOptions = orderCompletionMail(order, userDetails);
        mailOptions.to = userDetails.email;
        mailOptions.from = process.env.EMAIL_FROM;

        await sendgrid.send(mailOptions);
      } catch (err) {
        console.error("Error sending order completion email:", err);
      }

      await notifyUser({
        userId: order.user,
        message: "Your order has been approved and completed.",
        title: "Order Approved",
        entityId: order._id,
        entityType: "order"
      });
    }

    // Step 7: Always notify of order confirmation
    notifyUser({
      userId: payerReference,
      message: "Your order has been successfully placed.",
      title: "Order Confirmed",
      entityId: order._id,
      entityType: "order",
    }).catch((err) =>
        console.error("Notification failed:", err.message)
    );

    // Step 8: Return response
    return res.status(201).json({
      status: "success",
      message: "Order created successfully",
      orderId: order._id,
      summary: {
        subtotal,
        serviceFee,
        total,
        savings,
        items: orderItems.length,
      },
    });
  } catch (error) {
    console.error("Order processing error:", {
      error: error.message,
      stack: error.stack,
      userId: payerReference,
    });

    return res.status(500).json({
      error: "Order processing failed",
      details: process.env.NODE_ENV === "development" ? error.message : undefined,
    });
  }
}