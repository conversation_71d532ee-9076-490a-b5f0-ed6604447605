// homepageConfigController.js
import * as factory from "./handlerFactory.js";
import HomepageConfig from "./../models/homeConfigModel.js";

// Create a new homepage configuration
export const createHomepageConfig = factory.createOne(HomepageConfig);
// Get a homepage configuration by ID
export const getHomepageConfig = factory.getOne(HomepageConfig);
// Update a homepage configuration by ID
export const updateHomepageConfig = factory.updateOne(HomepageConfig);
// Delete a homepage configuration by ID
export const deleteHomepageConfig = factory.deleteOne(HomepageConfig);
