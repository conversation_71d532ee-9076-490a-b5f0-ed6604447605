import EUTax from "../models/taxModel.js";

export const getAllTax = async (req, res) => {
    try {
        const taxes = await EUTax.find().sort({ country: 1 });
        res.status(200).json({ status: 'success', data: taxes });
    } catch (error) {
        console.error('Error fetching taxes:', error);
        res.status(500).json({ status: 'error', message: 'Internal Server Error' });
    }
};

export const patchTax = async (req, res) => {
    const { taxPercentage } = req.body;
    const taxId = req.params.id;

    try {
        const updated = await EUTax.findByIdAndUpdate(taxId,{ taxPercentage }, { new: true });

        res.status(200).json({
            status: 'success',
            data: updated,
        });
    } catch (error) {
        console.error('Error updating tax:', error);
        res.status(500).json({ status: 'error', message: 'Internal Server Error' });
    }
};

export const postTaxes = async (request, response) => {
    try {
        const bulkOps = euTaxRates.map(({ country, taxPercentage }) => ({
            updateOne: {
                filter: { country },
                update: { $set: { taxPercentage } },
                upsert: true,
            },
        }));

        await EUTax.bulkWrite(bulkOps);
        const updatedTaxes = await EUTax.find();
        response.status(201).json({ data:  updatedTaxes});
    } catch (error) {
        console.error(error);
        response.status(500).json({ error: 'Failed to save EU tax rates' });
    }
};

export const getTaxByCountry = async (request, response) => {
    try {
        const tax = await EUTax.findOne({ country: request.params.country });
        response.status(201).json({ status: 'success', taxPercentage: tax?.taxPercentage});
    } catch (error) {
        console.error(error);
        response.status(500).json({ error: 'Failed to serve EU tax rates' });
    }
};

var euTaxRates = [
    { country: 'Germany', taxPercentage: 19 },
    { country: 'France', taxPercentage: 20 },
    { country: 'Italy', taxPercentage: 22 },
    { country: 'Spain', taxPercentage: 21 },
    { country: 'Netherlands', taxPercentage: 21 },
    { country: 'Belgium', taxPercentage: 21 },
    { country: 'Austria', taxPercentage: 20 },
    { country: 'Portugal', taxPercentage: 23 },
    { country: 'Sweden', taxPercentage: 25 },
    { country: 'Denmark', taxPercentage: 25 },
    { country: 'Finland', taxPercentage: 24 },
    { country: 'Ireland', taxPercentage: 23 },
    { country: 'Poland', taxPercentage: 23 },
    { country: 'Czech Republic', taxPercentage: 21 },
    { country: 'Hungary', taxPercentage: 27 },
    { country: 'Romania', taxPercentage: 19 },
    { country: 'Slovakia', taxPercentage: 20 },
    { country: 'Slovenia', taxPercentage: 22 },
    { country: 'Greece', taxPercentage: 24 },
    { country: 'Luxembourg', taxPercentage: 17 },
    { country: 'Bulgaria', taxPercentage: 20 },
    { country: 'Croatia', taxPercentage: 25 },
    { country: 'Estonia', taxPercentage: 20 },
    { country: 'Latvia', taxPercentage: 21 },
    { country: 'Lithuania', taxPercentage: 21 },
    { country: 'Cyprus', taxPercentage: 19 },
    { country: 'Malta', taxPercentage: 18 },
];