import Pusher from 'pusher';
import Notification from "../models/notificationModel.js";
import NotificationModel from "../models/notificationModel.js";
import Order from "../models/OrderModel.js";
import Offer from "../models/offerModel.js";
import Template from "../models/templateModel.js";

export const pusher = new Pusher({
    appId: process.env.PUSHER_ID ?? "*******",
    key: process.env.PUSHER_KEY ?? "09b1adba58d8659c01d4",
    secret: process.env.PUSHER_SECRET ?? "d7b085a06ebed7262350",
    cluster: 'ap2',
    useTLS: true,
});

export const notifyUser = async ({userId, title, message, entityType, entityId}) => {

    try {
        const notification = await Notification.create({
            userId,
            title,
            message,
            entityType,
            entityId,
        });

        let entity = null;

        if (notification.entityType === 'order') {
            const order = await Order.findById(notification.entityId);

            if (order && order.items && order.items.length > 0) {
                const offerId = order.items[0].offer;
                const offer = await Offer.findById(offerId);
                if (offer) {
                    const template = await Template.findById(offer.template);
                    entity = {
                        _id: offer._id,
                        templateCoverImage: template?.coverImage || null,
                    };
                }
            }

        } else if (notification.entityType === 'review' || notification.entityType === 'wishlist') {
            const offer = await Offer.findById(notification.entityId);
            if (offer) {
                const template = await Template.findById(offer.template);
                entity = {
                    _id: offer._id,
                    templateCoverImage: template?.coverImage || null,
                };
            }
        }

        const fullNotification = {
            ...notification.toObject(),
            entity,
        };

        await pusher.trigger(`private-user-${userId}`, 'new-notification', fullNotification);

        return fullNotification;
    }
    catch (error) {
        console.log(error, "notification service")    
    }
};