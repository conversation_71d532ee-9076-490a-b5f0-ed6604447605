import mongoose from "mongoose";

const HELP_CATEGORIES = [
  "Accounts",
  "Vendors",
  "Orders & Returns",
  "Shipping & Delivery",
  "Earnings & Payouts",
  "payment",
];

const HelpQuestionSchema = new mongoose.Schema({
  title: { type: String, required: true },
  content: { type: String, required: true },
  isActive: { type: Boolean, default: true },
  createdAt: { type: Date, default: Date.now },
});

const HelpSubcategorySchema = new mongoose.Schema({
  title: { type: String },
  questions: [HelpQuestionSchema],
  isActive: { type: Boolean, default: true },
  createdAt: { type: Date, default: Date.now },
});

const HelpCategorySchema = new mongoose.Schema({
  title: { type: String, enum: HELP_CATEGORIES, unique: true },
  subcategories: [HelpSubcategorySchema],
  isActive: { type: Boolean, default: true },
  createdAt: { type: Date, default: Date.now },
});

const HelpCategory = mongoose.model("HelpCategory", HelpCategorySchema);
export default HelpCategory;
